'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var core = require('@vueuse/core');
var index = require('../../../../hooks/use-namespace/index.js');
var aria = require('../../../../constants/aria.js');

function useKeydown({ el$ }, store) {
  const ns = index.useNamespace("tree");
  vue.onMounted(() => {
    initTabIndex();
  });
  vue.onUpdated(() => {
    const checkboxItems = Array.from(el$.value.querySelectorAll("input[type=checkbox]"));
    checkboxItems.forEach((checkbox) => {
      checkbox.setAttribute("tabindex", "-1");
    });
  });
  function canNodeFocus(treeItems, nextIndex) {
    var _a, _b;
    const currentNode = store.value.getNode(treeItems[nextIndex].dataset.key);
    return currentNode.canFocus && currentNode.visible && (((_a = currentNode.parent) == null ? void 0 : _a.expanded) || ((_b = currentNode.parent) == null ? void 0 : _b.level) === 0);
  }
  const handleKeydown = (ev) => {
    const currentItem = ev.target;
    if (!currentItem.className.includes(ns.b("node")))
      return;
    const code = ev.code;
    const treeItems = Array.from(el$.value.querySelectorAll(`.${ns.is("focusable")}[role=treeitem]`));
    const currentIndex = treeItems.indexOf(currentItem);
    let nextIndex;
    if ([aria.EVENT_CODE.up, aria.EVENT_CODE.down].includes(code)) {
      ev.preventDefault();
      if (code === aria.EVENT_CODE.up) {
        nextIndex = currentIndex === -1 ? 0 : currentIndex !== 0 ? currentIndex - 1 : treeItems.length - 1;
        const startIndex = nextIndex;
        while (true) {
          if (canNodeFocus(treeItems, nextIndex)) {
            break;
          }
          nextIndex--;
          if (nextIndex === startIndex) {
            nextIndex = -1;
            break;
          }
          if (nextIndex < 0) {
            nextIndex = treeItems.length - 1;
          }
        }
      } else {
        nextIndex = currentIndex === -1 ? 0 : currentIndex < treeItems.length - 1 ? currentIndex + 1 : 0;
        const startIndex = nextIndex;
        while (true) {
          if (canNodeFocus(treeItems, nextIndex)) {
            break;
          }
          nextIndex++;
          if (nextIndex === startIndex) {
            nextIndex = -1;
            break;
          }
          if (nextIndex >= treeItems.length) {
            nextIndex = 0;
          }
        }
      }
      nextIndex !== -1 && treeItems[nextIndex].focus();
    }
    if ([aria.EVENT_CODE.left, aria.EVENT_CODE.right].includes(code)) {
      ev.preventDefault();
      currentItem.click();
    }
    const hasInput = currentItem.querySelector('[type="checkbox"]');
    if ([aria.EVENT_CODE.enter, aria.EVENT_CODE.numpadEnter, aria.EVENT_CODE.space].includes(code) && hasInput) {
      ev.preventDefault();
      hasInput.click();
    }
  };
  core.useEventListener(el$, "keydown", handleKeydown);
  const initTabIndex = () => {
    var _a;
    if (!el$.value)
      return;
    const treeItems = Array.from(el$.value.querySelectorAll(`.${ns.is("focusable")}[role=treeitem]`));
    const checkboxItems = Array.from(el$.value.querySelectorAll("input[type=checkbox]"));
    checkboxItems.forEach((checkbox) => {
      checkbox.setAttribute("tabindex", "-1");
    });
    const checkedItem = el$.value.querySelectorAll(`.${ns.is("checked")}[role=treeitem]`);
    if (checkedItem.length) {
      checkedItem[0].setAttribute("tabindex", "0");
      return;
    }
    (_a = treeItems[0]) == null ? void 0 : _a.setAttribute("tabindex", "0");
  };
}

exports.useKeydown = useKeydown;
//# sourceMappingURL=useKeydown.js.map
