version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./src/frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    volumes:
      - ./src/frontend/dist:/app/dist
    depends_on:
      - backend
    networks:
      - novel-workflow

  # 后端服务
  backend:
    build:
      context: ./src/backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
      - DB_PATH=/app/data/novel_workflow.db
      - UPLOAD_PATH=/app/uploads
      - LOG_FILE=/app/logs/app.log
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    networks:
      - novel-workflow

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./src/frontend/dist:/usr/share/nginx/html
    depends_on:
      - frontend
      - backend
    networks:
      - novel-workflow

networks:
  novel-workflow:
    driver: bridge

volumes:
  data:
  uploads:
  logs:
