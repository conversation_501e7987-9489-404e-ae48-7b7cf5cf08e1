{"name": "玄幻小说创作工作流", "description": "完整的玄幻小说创作流程，从世界观设定到章节生成", "type": "custom", "category": "玄幻", "is_template": true, "variables": {"novelTitle": "小说标题", "mainCharacterName": "主角姓名", "worldType": "世界类型", "powerSystem": "力量体系"}, "nodes": [{"id": "start", "type": "start", "name": "开始", "position": {"x": 100, "y": 100}, "config": {}, "description": "工作流开始节点"}, {"id": "world-setup", "type": "world_setup", "name": "世界观设定", "position": {"x": 300, "y": 100}, "config": {"temperature": 0.8, "max_tokens": 3000}, "promptTemplate": "请为玄幻小说《{{novelTitle}}》创建详细的世界观设定：\n\n世界类型：{{worldType}}\n力量体系：{{powerSystem}}\n\n请包含以下内容：\n1. 世界历史背景\n2. 地理环境和重要地点\n3. 修炼体系和等级划分\n4. 主要势力和组织\n5. 特殊物品和宝物\n6. 世界规则和法则\n\n要求：内容丰富，逻辑自洽，具有玄幻特色。", "inputSchema": {"required": ["novel<PERSON>itle", "worldType", "powerSystem"]}, "outputSchema": {"properties": {"worldBackground": "世界背景", "geography": "地理环境", "powerSystem": "力量体系", "factions": "势力组织", "artifacts": "特殊物品", "rules": "世界规则"}}}, {"id": "main-character", "type": "character_setup", "name": "主角设定", "position": {"x": 500, "y": 100}, "config": {"temperature": 0.7, "max_tokens": 2500}, "promptTemplate": "请为玄幻小说《{{novelTitle}}》创建主角角色设定：\n\n角色名称：{{mainCharacterName}}\n世界背景：{{worldBackground}}\n力量体系：{{powerSystem}}\n\n请包含以下内容：\n1. 基本信息（年龄、出身、身份）\n2. 外貌特征\n3. 性格特点和价值观\n4. 初始实力和天赋\n5. 成长背景和重要经历\n6. 目标和动机\n7. 优势和弱点\n8. 特殊能力或血脉\n\n要求：角色鲜明，符合玄幻设定，具有成长潜力。", "inputSchema": {"required": ["mainCharacterName", "worldBackground", "powerSystem"]}, "outputSchema": {"properties": {"name": "角色姓名", "age": "年龄", "appearance": "外貌", "personality": "性格", "background": "背景", "abilities": "能力", "goals": "目标", "weaknesses": "弱点"}}}, {"id": "supporting-characters", "type": "character_setup", "name": "配角设定", "position": {"x": 700, "y": 100}, "config": {"temperature": 0.7, "max_tokens": 2000}, "promptTemplate": "请为玄幻小说《{{novelTitle}}》创建重要配角设定：\n\n主角信息：{{mainCharacter}}\n世界背景：{{worldBackground}}\n\n请创建3-5个重要配角，包含：\n1. 师父/导师角色\n2. 同伴/朋友角色\n3. 对手/敌人角色\n4. 红颜知己角色\n5. 神秘长者角色\n\n每个角色包含：\n- 姓名和身份\n- 外貌特征\n- 性格特点\n- 实力等级\n- 与主角的关系\n- 在故事中的作用\n\n要求：角色多样化，关系网复杂，推动剧情发展。", "inputSchema": {"required": ["mainCharacter", "worldBackground"]}}, {"id": "plot-outline", "type": "plot_outline", "name": "情节大纲", "position": {"x": 300, "y": 300}, "config": {"temperature": 0.6, "max_tokens": 4000}, "promptTemplate": "请为玄幻小说《{{novelTitle}}》创建详细的情节大纲：\n\n主角设定：{{mainCharacter}}\n配角设定：{{supportingCharacters}}\n世界背景：{{worldBackground}}\n\n请按以下结构创建大纲：\n\n第一卷：起源篇（1-30章）\n- 主角出场和初始设定\n- 获得机缘或觉醒能力\n- 初次历练和成长\n- 建立基础人际关系\n\n第二卷：成长篇（31-60章）\n- 进入更大的世界\n- 遇到强敌和挑战\n- 实力快速提升\n- 卷入重大事件\n\n第三卷：争霸篇（61-90章）\n- 参与势力争斗\n- 面对生死危机\n- 获得重要机缘\n- 确立江湖地位\n\n每卷包含：\n- 主要情节线\n- 重要事件节点\n- 角色发展弧线\n- 冲突和转折点\n\n要求：情节紧凑，冲突激烈，符合玄幻小说特点。", "inputSchema": {"required": ["mainCharacter", "supportingCharacters", "worldBackground"]}}, {"id": "chapter-1", "type": "chapter_generate", "name": "第一章生成", "position": {"x": 500, "y": 300}, "config": {"temperature": 0.7, "max_tokens": 3000}, "promptTemplate": "请根据以下信息写出玄幻小说《{{novelTitle}}》的第一章：\n\n章节标题：{{chapterTitle}}\n主角设定：{{mainCharacter}}\n世界背景：{{worldBackground}}\n情节大纲：{{plotOutline}}\n\n写作要求：\n1. 字数：2500-3000字\n2. 开篇要有吸引力，快速进入情节\n3. 展现世界观和主角特点\n4. 设置悬念和冲突\n5. 语言生动，描写细腻\n6. 符合玄幻小说风格\n\n章节结构：\n- 开场：环境描写或动作场面\n- 主角出场：展现性格和能力\n- 事件发生：推动情节发展\n- 结尾：设置悬念或转折\n\n请写出完整的章节内容。", "inputSchema": {"required": ["chapterTitle", "mainCharacter", "worldBackground", "plotOutline"]}}, {"id": "content-polish", "type": "content_polish", "name": "内容润色", "position": {"x": 700, "y": 300}, "config": {"temperature": 0.5, "max_tokens": 3500}, "promptTemplate": "请对以下玄幻小说章节进行润色和改进：\n\n原文：\n{{chapterContent}}\n\n润色要求：\n1. 改进语言表达，使其更加流畅自然\n2. 优化描写细节，增强画面感\n3. 完善对话内容，使其更符合角色性格\n4. 调整节奏，增强阅读体验\n5. 修正语法错误和用词不当\n6. 保持玄幻小说的风格特色\n7. 确保情节逻辑合理\n\n请返回润色后的完整章节内容。", "inputSchema": {"required": ["chapterContent"]}}, {"id": "quality-check", "type": "condition", "name": "质量检查", "position": {"x": 500, "y": 500}, "config": {"condition": {"field": "polishedContent", "operator": "word_count_greater", "value": 2000}}, "description": "检查润色后的内容是否达到质量要求"}, {"id": "end", "type": "end", "name": "完成", "position": {"x": 700, "y": 500}, "config": {}, "description": "工作流结束节点"}], "edges": [{"id": "edge1", "source": "start", "target": "world-setup", "type": "default"}, {"id": "edge2", "source": "world-setup", "target": "main-character", "type": "data_flow", "dataMapping": {"worldBackground": "worldBackground", "powerSystem": "powerSystem"}}, {"id": "edge3", "source": "main-character", "target": "supporting-characters", "type": "data_flow", "dataMapping": {"mainCharacter": "character"}}, {"id": "edge4", "source": "supporting-characters", "target": "plot-outline", "type": "data_flow", "dataMapping": {"supportingCharacters": "characters"}}, {"id": "edge5", "source": "plot-outline", "target": "chapter-1", "type": "data_flow", "dataMapping": {"plotOutline": "outline"}}, {"id": "edge6", "source": "chapter-1", "target": "content-polish", "type": "data_flow", "dataMapping": {"chapterContent": "content"}}, {"id": "edge7", "source": "content-polish", "target": "quality-check", "type": "data_flow", "dataMapping": {"polishedContent": "content"}}, {"id": "edge8", "source": "quality-check", "target": "end", "type": "conditional", "condition": {"field": "result", "operator": "equals", "value": true}}]}