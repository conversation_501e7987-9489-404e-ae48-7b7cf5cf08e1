<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button @click="saveWorkflow" type="primary">
          <el-icon><Document /></el-icon>
          保存工作流
        </el-button>
        <el-button @click="runWorkflow" type="success">
          <el-icon><VideoPlay /></el-icon>
          运行
        </el-button>
        <el-button @click="clearCanvas">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
      
      <div class="toolbar-center">
        <el-button @click="showApiSettings" type="info">
          <el-icon><Setting /></el-icon>
          API配置
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="zoomIn">放大</el-button>
        <el-button @click="zoomOut">缩小</el-button>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>

    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <div class="node-panel">
        <div class="panel-section">
          <h3>节点库</h3>
          <div class="node-category">
            <h4>输入节点</h4>
            <div 
              class="node-item" 
              v-for="node in inputNodes" 
              :key="node.type"
              @dragstart="onDragStart($event, node)"
              draggable="true"
            >
              <div class="node-icon" :style="{ background: node.color }">
                {{ node.icon }}
              </div>
              <span>{{ node.name }}</span>
            </div>
          </div>
          
          <div class="node-category">
            <h4>AI处理</h4>
            <div 
              class="node-item" 
              v-for="node in aiNodes" 
              :key="node.type"
              @dragstart="onDragStart($event, node)"
              draggable="true"
            >
              <div class="node-icon" :style="{ background: node.color }">
                {{ node.icon }}
              </div>
              <span>{{ node.name }}</span>
            </div>
          </div>
          
          <div class="node-category">
            <h4>输出节点</h4>
            <div 
              class="node-item" 
              v-for="node in outputNodes" 
              :key="node.type"
              @dragstart="onDragStart($event, node)"
              draggable="true"
            >
              <div class="node-icon" :style="{ background: node.color }">
                {{ node.icon }}
              </div>
              <span>{{ node.name }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div 
          class="canvas" 
          ref="canvasRef"
          @drop="onDrop"
          @dragover.prevent
          @click="clearSelection"
          :style="{ transform: `scale(${zoomLevel})` }"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 连接线 -->
          <svg class="connections" :style="{ transform: `scale(${1/zoomLevel})` }">
            <path
              v-for="connection in connections"
              :key="`${connection.from}-${connection.to}`"
              :d="getConnectionPath(connection)"
              stroke="#409eff"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
            />
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
              </marker>
            </defs>
          </svg>
          
          <!-- 工作流节点 -->
          <div
            v-for="node in workflowNodes"
            :key="node.id"
            class="workflow-node"
            :class="{ 
              'selected': selectedNode === node.id,
              'running': runningNodes.includes(node.id)
            }"
            :style="{ 
              left: node.x + 'px', 
              top: node.y + 'px',
              background: node.color 
            }"
            @click.stop="selectNode(node.id)"
            @mousedown="startDrag(node.id, $event)"
          >
            <div class="node-header">
              <span class="node-icon">{{ node.icon }}</span>
              <span class="node-title">{{ node.name }}</span>
              <el-button 
                class="delete-btn" 
                size="small" 
                type="danger" 
                @click.stop="deleteNode(node.id)"
              >
                ×
              </el-button>
            </div>
            
            <!-- 输入连接点 -->
            <div 
              v-if="node.inputs > 0"
              class="input-port"
              @click.stop="connectPort(node.id, 'input')"
            ></div>
            
            <!-- 输出连接点 -->
            <div 
              v-if="node.outputs > 0"
              class="output-port"
              @click.stop="connectPort(node.id, 'output')"
            ></div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <div class="panel-section">
          <h3>节点属性</h3>
          <div v-if="selectedNodeData">
            <el-form :model="selectedNodeData" label-width="80px">
              <el-form-item label="节点名称">
                <el-input v-model="selectedNodeData.name" />
              </el-form-item>
              
              <el-form-item label="描述">
                <el-input 
                  v-model="selectedNodeData.description" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>
              
              <!-- AI节点特殊配置 -->
              <template v-if="selectedNodeData.type.includes('ai')">
                <el-form-item label="模型">
                  <el-select v-model="selectedNodeData.model">
                    <el-option label="GPT-3.5" value="gpt-3.5" />
                    <el-option label="GPT-4" value="gpt-4" />
                    <el-option label="Llama2" value="llama2" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="提示词">
                  <el-input 
                    v-model="selectedNodeData.prompt" 
                    type="textarea" 
                    :rows="4"
                    placeholder="输入AI提示词..."
                  />
                </el-form-item>
              </template>
            </el-form>
          </div>
          <div v-else class="no-selection">
            <p>请选择一个节点来编辑属性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="apiDialogVisible"
      title="API配置"
      width="600px"
      :before-close="handleApiDialogClose"
    >
      <el-form :model="apiConfig" label-width="120px">
        <el-form-item label="OpenAI API Key">
          <el-input
            v-model="apiConfig.openaiKey"
            type="password"
            placeholder="请输入OpenAI API Key"
            show-password
          />
        </el-form-item>

        <el-form-item label="OpenAI Base URL">
          <el-input
            v-model="apiConfig.openaiBaseUrl"
            placeholder="https://api.openai.com/v1"
          />
        </el-form-item>

        <el-form-item label="默认模型">
          <el-select v-model="apiConfig.defaultModel" placeholder="选择默认模型">
            <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
            <el-option label="GPT-4" value="gpt-4" />
            <el-option label="GPT-4 Turbo" value="gpt-4-turbo" />
            <el-option label="GPT-4o" value="gpt-4o" />
          </el-select>
        </el-form-item>

        <el-form-item label="Claude API Key">
          <el-input
            v-model="apiConfig.claudeKey"
            type="password"
            placeholder="请输入Claude API Key"
            show-password
          />
        </el-form-item>

        <el-form-item label="Claude Base URL">
          <el-input
            v-model="apiConfig.claudeBaseUrl"
            placeholder="https://api.anthropic.com"
          />
        </el-form-item>

        <el-form-item label="本地模型URL">
          <el-input
            v-model="apiConfig.localModelUrl"
            placeholder="http://localhost:11434"
          />
        </el-form-item>

        <el-form-item label="请求超时(秒)">
          <el-input-number
            v-model="apiConfig.timeout"
            :min="10"
            :max="300"
          />
        </el-form-item>

        <el-form-item label="最大重试次数">
          <el-input-number
            v-model="apiConfig.maxRetries"
            :min="0"
            :max="5"
          />
        </el-form-item>

        <el-divider>自定义API配置</el-divider>

        <div class="custom-apis">
          <div class="custom-api-header">
            <span>高级用户可自定义API地址和模型参数</span>
            <el-button type="primary" size="small" @click="addCustomApi">
              <el-icon><Plus /></el-icon>
              添加自定义API
            </el-button>
          </div>

          <div
            v-for="(customApi, index) in apiConfig.customApis"
            :key="index"
            class="custom-api-item"
          >
            <el-card>
              <template #header>
                <div class="custom-api-title">
                  <span>自定义API #{{ index + 1 }}</span>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeCustomApi(index)"
                  >
                    删除
                  </el-button>
                </div>
              </template>

              <el-form :model="customApi" label-width="100px" size="small">
                <el-form-item label="API名称">
                  <el-input v-model="customApi.name" placeholder="例如: DeepSeek" />
                </el-form-item>

                <el-form-item label="API密钥">
                  <el-input
                    v-model="customApi.apiKey"
                    type="password"
                    placeholder="请输入API密钥"
                    show-password
                  />
                </el-form-item>

                <el-form-item label="API地址">
                  <el-input
                    v-model="customApi.baseUrl"
                    placeholder="https://api.example.com/v1"
                  />
                </el-form-item>

                <el-form-item label="可用模型">
                  <div class="model-tags">
                    <el-tag
                      v-for="model in customApi.models"
                      :key="model"
                      closable
                      @close="removeModel(customApi, model)"
                      class="model-tag"
                    >
                      {{ model }}
                    </el-tag>
                    <el-input
                      v-if="customApi.inputVisible"
                      ref="modelInputRef"
                      v-model="customApi.inputValue"
                      size="small"
                      @keyup.enter="confirmModel(customApi)"
                      @blur="confirmModel(customApi)"
                      class="model-input"
                    />
                    <el-button
                      v-else
                      size="small"
                      @click="showModelInput(customApi)"
                    >
                      + 添加模型
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item label="最大Token">
                  <el-input-number
                    v-model="customApi.maxTokens"
                    :min="1000"
                    :max="200000"
                    :step="1000"
                  />
                </el-form-item>

                <el-form-item label="创造性">
                  <el-slider
                    v-model="customApi.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    show-input
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="testApiConnection" :loading="testingApi">
            测试连接
          </el-button>
          <el-button @click="apiDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveApiConfig">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, VideoPlay, Delete, Setting, Plus } from '@element-plus/icons-vue'

// 响应式数据
const canvasRef = ref()
const workflowNodes = ref([])
const connections = ref([])
const selectedNode = ref(null)
const zoomLevel = ref(1)
const draggedNode = ref(null)
const connectingFrom = ref(null)
const runningNodes = ref([])

// API配置相关
const apiDialogVisible = ref(false)
const testingApi = ref(false)
const apiConfig = ref({
  openaiKey: '',
  openaiBaseUrl: 'https://api.openai.com/v1',
  defaultModel: 'gpt-3.5-turbo',
  claudeKey: '',
  claudeBaseUrl: 'https://api.anthropic.com',
  localModelUrl: 'http://localhost:11434',
  timeout: 60,
  maxRetries: 3,
  customApis: []
})

// 节点类型定义
const inputNodes = ref([
  { type: 'text-input', name: '文本输入', icon: '📝', color: '#67C23A', inputs: 0, outputs: 1 },
  { type: 'file-input', name: '文件输入', icon: '📁', color: '#67C23A', inputs: 0, outputs: 1 },
  { type: 'user-input', name: '用户输入', icon: '👤', color: '#67C23A', inputs: 0, outputs: 1 }
])

const aiNodes = ref([
  { type: 'ai-generate', name: 'AI生成', icon: '🤖', color: '#409EFF', inputs: 1, outputs: 1 },
  { type: 'ai-rewrite', name: 'AI改写', icon: '✏️', color: '#E6A23C', inputs: 1, outputs: 1 },
  { type: 'ai-analyze', name: 'AI分析', icon: '🔍', color: '#F56C6C', inputs: 1, outputs: 1 },
  { type: 'ai-translate', name: 'AI翻译', icon: '🌐', color: '#909399', inputs: 1, outputs: 1 },
  { type: 'character-gen', name: '角色生成', icon: '👥', color: '#9C27B0', inputs: 1, outputs: 1 }
])

const outputNodes = ref([
  { type: 'text-output', name: '文本输出', icon: '📄', color: '#67C23A', inputs: 1, outputs: 0 },
  { type: 'file-output', name: '文件输出', icon: '💾', color: '#67C23A', inputs: 1, outputs: 0 },
  { type: 'preview', name: '预览', icon: '👁️', color: '#409EFF', inputs: 1, outputs: 0 }
])

// 计算属性
const selectedNodeData = computed(() => {
  if (!selectedNode.value) return null
  return workflowNodes.value.find(node => node.id === selectedNode.value)
})

// 拖拽开始
const onDragStart = (event, nodeType) => {
  draggedNode.value = nodeType
}

// 放置节点
const onDrop = (event) => {
  if (!draggedNode.value) return
  
  const rect = canvasRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value - 75
  const y = (event.clientY - rect.top) / zoomLevel.value - 40
  
  const newNode = {
    id: Date.now().toString(),
    ...draggedNode.value,
    x: Math.max(0, x),
    y: Math.max(0, y),
    model: 'gpt-3.5',
    prompt: '',
    description: ''
  }
  
  workflowNodes.value.push(newNode)
  draggedNode.value = null
}

// 选择节点
const selectNode = (nodeId) => {
  selectedNode.value = nodeId
}

// 清除选择
const clearSelection = () => {
  selectedNode.value = null
  connectingFrom.value = null
}

// 删除节点
const deleteNode = (nodeId) => {
  workflowNodes.value = workflowNodes.value.filter(node => node.id !== nodeId)
  connections.value = connections.value.filter(
    conn => conn.from !== nodeId && conn.to !== nodeId
  )
  if (selectedNode.value === nodeId) {
    selectedNode.value = null
  }
}

// 连接端口
const connectPort = (nodeId, portType) => {
  if (!connectingFrom.value) {
    if (portType === 'output') {
      connectingFrom.value = nodeId
    }
  } else {
    if (portType === 'input' && connectingFrom.value !== nodeId) {
      // 检查是否已存在连接
      const existingConnection = connections.value.find(
        conn => conn.from === connectingFrom.value && conn.to === nodeId
      )
      
      if (!existingConnection) {
        connections.value.push({
          from: connectingFrom.value,
          to: nodeId
        })
      }
    }
    connectingFrom.value = null
  }
}

// 获取连接路径
const getConnectionPath = (connection) => {
  const fromNode = workflowNodes.value.find(n => n.id === connection.from)
  const toNode = workflowNodes.value.find(n => n.id === connection.to)
  
  if (!fromNode || !toNode) return ''
  
  const fromX = fromNode.x + 150
  const fromY = fromNode.y + 40
  const toX = toNode.x
  const toY = toNode.y + 40
  
  const midX = (fromX + toX) / 2
  
  return `M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`
}

// 缩放控制
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

// 清空画布
const clearCanvas = () => {
  workflowNodes.value = []
  connections.value = []
  selectedNode.value = null
}

// 保存工作流
const saveWorkflow = () => {
  const workflow = {
    nodes: workflowNodes.value,
    connections: connections.value
  }
  localStorage.setItem('workflow', JSON.stringify(workflow))
  ElMessage.success('工作流已保存')
}

// 运行工作流
const runWorkflow = async () => {
  if (workflowNodes.value.length === 0) {
    ElMessage.warning('请先添加节点')
    return
  }
  
  ElMessage.info('开始运行工作流...')
  
  // 模拟运行过程
  for (const node of workflowNodes.value) {
    runningNodes.value.push(node.id)
    await new Promise(resolve => setTimeout(resolve, 1000))
    runningNodes.value = runningNodes.value.filter(id => id !== node.id)
  }
  
  ElMessage.success('工作流运行完成')
}

// 节点拖拽
let isDragging = false
let dragOffset = { x: 0, y: 0 }

const startDrag = (nodeId, event) => {
  const node = workflowNodes.value.find(n => n.id === nodeId)
  if (!node) return
  
  isDragging = true
  dragOffset.x = event.clientX - node.x * zoomLevel.value
  dragOffset.y = event.clientY - node.y * zoomLevel.value
  
  const onMouseMove = (e) => {
    if (!isDragging) return
    node.x = (e.clientX - dragOffset.x) / zoomLevel.value
    node.y = (e.clientY - dragOffset.y) / zoomLevel.value
  }
  
  const onMouseUp = () => {
    isDragging = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// API配置相关方法
const showApiSettings = () => {
  apiDialogVisible.value = true
}

const saveApiConfig = () => {
  localStorage.setItem('apiConfig', JSON.stringify(apiConfig.value))
  ElMessage.success('API配置已保存')
  apiDialogVisible.value = false
}

const testApiConnection = async () => {
  testingApi.value = true
  try {
    // 这里可以添加实际的API测试逻辑
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('API连接测试成功')
  } catch (error) {
    ElMessage.error('API连接测试失败')
  } finally {
    testingApi.value = false
  }
}

const handleApiDialogClose = (done) => {
  done()
}

// 自定义API相关方法
const addCustomApi = () => {
  apiConfig.value.customApis.push({
    name: '',
    apiKey: '',
    baseUrl: '',
    models: ['gemini-2.5-pro'],
    maxTokens: 200000,
    temperature: 0.7,
    inputVisible: false,
    inputValue: ''
  })
}

const removeCustomApi = (index) => {
  apiConfig.value.customApis.splice(index, 1)
}

const showModelInput = (customApi) => {
  customApi.inputVisible = true
  nextTick(() => {
    // 聚焦到输入框 - 可以在这里添加聚焦逻辑
  })
}

const confirmModel = (customApi) => {
  if (customApi.inputValue && !customApi.models.includes(customApi.inputValue)) {
    customApi.models.push(customApi.inputValue)
  }
  customApi.inputVisible = false
  customApi.inputValue = ''
}

const removeModel = (customApi, model) => {
  const index = customApi.models.indexOf(model)
  if (index > -1) {
    customApi.models.splice(index, 1)
  }
}

// 组件挂载时加载保存的工作流和API配置
onMounted(() => {
  // 加载工作流
  const saved = localStorage.getItem('workflow')
  if (saved) {
    const workflow = JSON.parse(saved)
    workflowNodes.value = workflow.nodes || []
    connections.value = workflow.connections || []
  }

  // 加载API配置
  const savedApiConfig = localStorage.getItem('apiConfig')
  if (savedApiConfig) {
    apiConfig.value = { ...apiConfig.value, ...JSON.parse(savedApiConfig) }
  }
})
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a2e;
  color: white;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: #16213e;
  border-bottom: 1px solid #0f3460;
}

.workflow-title {
  color: #fff;
  font-weight: bold;
}

.zoom-level {
  margin-left: 10px;
  color: #ccc;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-panel {
  width: 250px;
  background: #16213e;
  border-right: 1px solid #0f3460;
  overflow-y: auto;
}

.panel-section {
  padding: 20px;
}

.panel-section h3 {
  margin: 0 0 20px 0;
  color: #fff;
}

.node-category h4 {
  margin: 15px 0 10px 0;
  color: #ccc;
  font-size: 14px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin: 5px 0;
  background: #0f3460;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.node-item:hover {
  background: #1e4d72;
  transform: translateX(5px);
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 12px;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #1a1a2e;
}

.canvas {
  position: relative;
  width: 2000px;
  height: 2000px;
  transform-origin: 0 0;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.workflow-node {
  position: absolute;
  width: 150px;
  min-height: 80px;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: move;
  z-index: 2;
  transition: all 0.2s;
}

.workflow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.workflow-node.running {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.5); }
  50% { box-shadow: 0 0 20px rgba(64, 158, 255, 0.8); }
  100% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.5); }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background: rgba(0,0,0,0.2);
  border-radius: 6px 6px 0 0;
  position: relative;
}

.node-header .node-icon {
  margin-right: 8px;
}

.node-title {
  flex: 1;
  font-size: 12px;
  font-weight: bold;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  padding: 0;
  font-size: 12px;
}

.input-port, .output-port {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #409eff;
  border: 2px solid white;
  cursor: pointer;
}

.input-port {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.output-port {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.property-panel {
  width: 300px;
  background: #16213e;
  border-left: 1px solid #0f3460;
  overflow-y: auto;
}

.no-selection {
  text-align: center;
  color: #ccc;
  padding: 40px 20px;
}

/* API配置对话框样式 */
.custom-apis {
  margin-top: 20px;
}

.custom-api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-color-warning-light-9);
  border-radius: 6px;
  border-left: 4px solid var(--el-color-warning);
}

.custom-api-header span {
  color: var(--el-color-warning-dark-2);
  font-size: 14px;
}

.custom-api-item {
  margin-bottom: 16px;
}

.custom-api-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.model-tag {
  margin: 0;
}

.model-input {
  width: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
