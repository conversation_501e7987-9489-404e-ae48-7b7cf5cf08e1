{"version": 3, "file": "dynamic-size-grid.js", "sources": ["../../../../../../../packages/components/virtual-list/src/components/dynamic-size-grid.ts"], "sourcesContent": ["import {\n  isFunction,\n  isNumber,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport createGrid from '../builders/build-grid'\nimport {\n  AUTO_ALIGNMENT,\n  CENTERED_ALIGNMENT,\n  DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n  END_ALIGNMENT,\n  SMART_ALIGNMENT,\n  START_ALIGNMENT,\n} from '../defaults'\n\nimport type { GridInstance } from '../builders/build-grid'\nimport type { VirtualizedGridProps } from '../props'\nimport type { Alignment, GridCache, ItemSize } from '../types'\n\nconst { max, min, floor } = Math\nconst SCOPE = 'ElDynamicSizeGrid'\n\ntype Props = VirtualizedGridProps\ntype CacheItemType = 'column' | 'row'\ntype Indices = {\n  columnIndex?: number\n  rowIndex?: number\n}\n\n// generates props access key via type\nconst ACCESS_SIZER_KEY_MAP = {\n  column: 'columnWidth',\n  row: 'rowHeight',\n} as const\n\n// generates cache access key via type\nconst ACCESS_LAST_VISITED_KEY_MAP = {\n  column: 'lastVisitedColumnIndex',\n  row: 'lastVisitedRowIndex',\n} as const\n\nconst getItemFromCache = (\n  props: Props,\n  index: number,\n  gridCache: GridCache,\n  type: CacheItemType\n) => {\n  const [cachedItems, sizer, lastVisited] = [\n    gridCache[type],\n    props[ACCESS_SIZER_KEY_MAP[type]] as ItemSize,\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]],\n  ]\n\n  if (index > lastVisited) {\n    let offset = 0\n    if (lastVisited >= 0) {\n      const item = cachedItems[lastVisited]\n      offset = item.offset + item.size\n    }\n\n    for (let i = lastVisited + 1; i <= index; i++) {\n      const size = sizer(i)\n\n      cachedItems[i] = {\n        offset,\n        size,\n      }\n\n      offset += size\n    }\n\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]] = index\n  }\n\n  return cachedItems[index]\n}\n\nconst bs = (\n  props: Props,\n  gridCache: GridCache,\n  low: number,\n  high: number,\n  offset: number,\n  type: CacheItemType\n) => {\n  while (low <= high) {\n    const mid = low + floor((high - low) / 2)\n    const currentOffset = getItemFromCache(props, mid, gridCache, type).offset\n\n    if (currentOffset === offset) {\n      return mid\n    } else if (currentOffset < offset) {\n      low = mid + 1\n    } else {\n      high = mid - 1\n    }\n  }\n\n  return max(0, low - 1)\n}\n\nconst es = (\n  props: Props,\n  gridCache: GridCache,\n  idx: number,\n  offset: number,\n  type: CacheItemType\n) => {\n  const total = type === 'column' ? props.totalColumn : props.totalRow\n  let exponent = 1\n\n  while (\n    idx < total &&\n    getItemFromCache(props, idx, gridCache, type).offset < offset\n  ) {\n    idx += exponent\n    exponent *= 2\n  }\n\n  return bs(props, gridCache, floor(idx / 2), min(idx, total - 1), offset, type)\n}\n\nconst findItem = (\n  props: Props,\n  gridCache: GridCache,\n  offset: number,\n  type: CacheItemType\n) => {\n  const [cache, lastVisitedIndex] = [\n    gridCache[type],\n    gridCache[ACCESS_LAST_VISITED_KEY_MAP[type]],\n  ]\n\n  const lastVisitedItemOffset =\n    lastVisitedIndex > 0 ? cache[lastVisitedIndex].offset : 0\n\n  if (lastVisitedItemOffset >= offset) {\n    return bs(props, gridCache, 0, lastVisitedIndex, offset, type)\n  }\n\n  return es(props, gridCache, max(0, lastVisitedIndex), offset, type)\n}\n\nconst getEstimatedTotalHeight = (\n  { totalRow }: Props,\n  { estimatedRowHeight, lastVisitedRowIndex, row }: GridCache\n) => {\n  let sizeOfVisitedRows = 0\n\n  if (lastVisitedRowIndex >= totalRow) {\n    lastVisitedRowIndex = totalRow - 1\n  }\n\n  if (lastVisitedRowIndex >= 0) {\n    const item = row[lastVisitedRowIndex]\n    sizeOfVisitedRows = item.offset + item.size\n  }\n\n  const unvisitedItems = totalRow - lastVisitedRowIndex - 1\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedRowHeight\n\n  return sizeOfVisitedRows + sizeOfUnvisitedItems\n}\nconst getEstimatedTotalWidth = (\n  { totalColumn }: Props,\n  { column, estimatedColumnWidth, lastVisitedColumnIndex }: GridCache\n) => {\n  let sizeOfVisitedColumns = 0\n\n  if (lastVisitedColumnIndex > totalColumn) {\n    lastVisitedColumnIndex = totalColumn - 1\n  }\n\n  if (lastVisitedColumnIndex >= 0) {\n    const item = column[lastVisitedColumnIndex]\n    sizeOfVisitedColumns = item.offset + item.size\n  }\n\n  const unvisitedItems = totalColumn - lastVisitedColumnIndex - 1\n  const sizeOfUnvisitedItems = unvisitedItems * estimatedColumnWidth\n\n  return sizeOfVisitedColumns + sizeOfUnvisitedItems\n}\n\nconst ACCESS_ESTIMATED_SIZE_KEY_MAP = {\n  column: getEstimatedTotalWidth,\n  row: getEstimatedTotalHeight,\n}\n\nconst getOffset = (\n  props: Props,\n  index: number,\n  alignment: Alignment,\n  scrollOffset: number,\n  cache: GridCache,\n  type: CacheItemType,\n  scrollBarWidth: number\n) => {\n  const [size, estimatedSizeAssociates] = [\n    type === 'row' ? props.height : props.width,\n    ACCESS_ESTIMATED_SIZE_KEY_MAP[type],\n  ] as [number, (props: Props, cache: GridCache) => number]\n  const item = getItemFromCache(props, index, cache, type)\n\n  const estimatedSize = estimatedSizeAssociates(props, cache)\n\n  const maxOffset = max(0, min(estimatedSize - size, item.offset))\n  const minOffset = max(0, item.offset - size + scrollBarWidth + item.size)\n\n  if (alignment === SMART_ALIGNMENT) {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      alignment = AUTO_ALIGNMENT\n    } else {\n      alignment = CENTERED_ALIGNMENT\n    }\n  }\n\n  switch (alignment) {\n    case START_ALIGNMENT: {\n      return maxOffset\n    }\n    case END_ALIGNMENT: {\n      return minOffset\n    }\n    case CENTERED_ALIGNMENT: {\n      return Math.round(minOffset + (maxOffset - minOffset) / 2)\n    }\n    case AUTO_ALIGNMENT:\n    default: {\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset\n      } else if (minOffset > maxOffset) {\n        return minOffset\n      } else if (scrollOffset < minOffset) {\n        return minOffset\n      } else {\n        return maxOffset\n      }\n    }\n  }\n}\n\nconst DynamicSizeGrid = createGrid({\n  name: 'ElDynamicSizeGrid',\n  getColumnPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, 'column')\n    return [item.size, item.offset]\n  },\n\n  getRowPosition: (props, idx, cache) => {\n    const item = getItemFromCache(props, idx, cache, 'row')\n    return [item.size, item.offset]\n  },\n\n  getColumnOffset: (\n    props,\n    columnIndex,\n    alignment,\n    scrollLeft,\n    cache,\n    scrollBarWidth\n  ) =>\n    getOffset(\n      props,\n      columnIndex,\n      alignment,\n      scrollLeft,\n      cache,\n      'column',\n      scrollBarWidth\n    ),\n\n  getRowOffset: (\n    props,\n    rowIndex,\n    alignment,\n    scrollTop,\n    cache,\n    scrollBarWidth: number\n  ) =>\n    getOffset(\n      props,\n      rowIndex,\n      alignment,\n      scrollTop,\n      cache,\n      'row',\n      scrollBarWidth\n    ),\n\n  getColumnStartIndexForOffset: (props, scrollLeft, cache) =>\n    findItem(props, cache, scrollLeft, 'column'),\n\n  getColumnStopIndexForStartIndex: (props, startIndex, scrollLeft, cache) => {\n    const item = getItemFromCache(props, startIndex, cache, 'column')\n\n    const maxOffset = scrollLeft + (props.width as number)\n\n    let offset = item.offset + item.size\n    let stopIndex = startIndex\n    while (stopIndex < props.totalColumn - 1 && offset < maxOffset) {\n      stopIndex++\n      offset += getItemFromCache(props, startIndex, cache, 'column').size\n    }\n    return stopIndex\n  },\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getRowStartIndexForOffset: (props, scrollTop, cache) =>\n    findItem(props, cache, scrollTop, 'row'),\n\n  getRowStopIndexForStartIndex: (props, startIndex, scrollTop, cache) => {\n    const { totalRow, height } = props\n    const item = getItemFromCache(props, startIndex, cache, 'row')\n    const maxOffset = scrollTop + (height as number)\n\n    let offset = item.size + item.offset\n    let stopIndex = startIndex\n\n    while (stopIndex < totalRow - 1 && offset < maxOffset) {\n      stopIndex++\n      offset += getItemFromCache(props, stopIndex, cache, 'row').size\n    }\n\n    return stopIndex\n  },\n  injectToInstance: (instance, cache) => {\n    const resetAfter = (\n      { columnIndex, rowIndex }: Indices,\n      forceUpdate?: boolean\n    ) => {\n      forceUpdate = isUndefined(forceUpdate) ? true : forceUpdate\n\n      if (isNumber(columnIndex)) {\n        cache.value.lastVisitedColumnIndex = Math.min(\n          cache.value.lastVisitedColumnIndex,\n          columnIndex - 1\n        )\n      }\n\n      if (isNumber(rowIndex)) {\n        cache.value.lastVisitedRowIndex = Math.min(\n          cache.value.lastVisitedRowIndex,\n          rowIndex - 1\n        )\n      }\n\n      instance.exposed?.getItemStyleCache.value(-1, null, null)\n\n      if (forceUpdate) instance.proxy?.$forceUpdate()\n    }\n\n    const resetAfterColumnIndex = (\n      columnIndex: number,\n      forceUpdate: boolean\n    ) => {\n      resetAfter(\n        {\n          columnIndex,\n        },\n        forceUpdate\n      )\n    }\n\n    const resetAfterRowIndex = (rowIndex: number, forceUpdate: boolean) => {\n      resetAfter(\n        {\n          rowIndex,\n        },\n        forceUpdate\n      )\n    }\n\n    Object.assign(instance.proxy!, {\n      resetAfterColumnIndex,\n      resetAfterRowIndex,\n      resetAfter,\n    })\n  },\n  initCache: ({\n    estimatedColumnWidth = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n    estimatedRowHeight = DEFAULT_DYNAMIC_LIST_ITEM_SIZE,\n  }) => {\n    const cache = {\n      column: {},\n      estimatedColumnWidth,\n      estimatedRowHeight,\n      lastVisitedColumnIndex: -1,\n      lastVisitedRowIndex: -1,\n      row: {},\n    } as GridCache\n\n    // TODO: expose methods.\n    return cache\n  },\n\n  clearCache: false,\n\n  validateProps: ({ columnWidth, rowHeight }) => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (!isFunction(columnWidth)) {\n        throwError(\n          SCOPE,\n          `\n          \"columnWidth\" must be passed as function,\n            instead ${typeof columnWidth} was given.\n        `\n        )\n      }\n\n      if (!isFunction(rowHeight)) {\n        throwError(\n          SCOPE,\n          `\n          \"rowHeight\" must be passed as function,\n            instead ${typeof rowHeight} was given.\n        `\n        )\n      }\n    }\n  },\n})\n\nexport default DynamicSizeGrid\n\nexport type ResetAfterIndex = (idx: number, forceUpdate: boolean) => void\nexport type ResetAfterIndices = (indices: Indices, forceUpdate: boolean) => void\n\nexport type DynamicSizeGridInstance = GridInstance & {\n  resetAfterColumnIndex: ResetAfterIndex\n  resetAfterRowIndex: ResetAfterIndex\n  resetAfter: ResetAfterIndices\n}\n"], "names": ["SMART_ALIGNMENT", "AUTO_ALIGNMENT", "CENTERED_ALIGNMENT", "START_ALIGNMENT", "END_ALIGNMENT", "createGrid", "isUndefined", "isNumber", "DEFAULT_DYNAMIC_LIST_ITEM_SIZE"], "mappings": ";;;;;;;;AAaA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;AAEjC,MAAM,oBAAoB,GAAG;AAC7B,EAAE,MAAM,EAAE,aAAa;AACvB,EAAE,GAAG,EAAE,WAAW;AAClB,CAAC,CAAC;AACF,MAAM,2BAA2B,GAAG;AACpC,EAAE,MAAM,EAAE,wBAAwB;AAClC,EAAE,GAAG,EAAE,qBAAqB;AAC5B,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,KAAK;AAC5D,EAAE,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,WAAW,CAAC,GAAG;AAC5C,IAAI,SAAS,CAAC,IAAI,CAAC;AACnB,IAAI,KAAK,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,GAAG,WAAW,EAAE;AAC3B,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,WAAW,IAAI,CAAC,EAAE;AAC1B,MAAM,MAAM,IAAI,GAAG,WAAW,CAAC,WAAW,CAAC,CAAC;AAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AACvC,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;AACnD,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG;AACvB,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR,MAAM,MAAM,IAAI,IAAI,CAAC;AACrB,KAAK;AACL,IAAI,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC;AACzD,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5B,CAAC,CAAC;AACF,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,KAAK;AAC1D,EAAE,OAAO,GAAG,IAAI,IAAI,EAAE;AACtB,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAC9C,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC;AAC/E,IAAI,IAAI,aAAa,KAAK,MAAM,EAAE;AAClC,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK,MAAM,IAAI,aAAa,GAAG,MAAM,EAAE;AACvC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AACpB,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC;AACrB,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC;AACF,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,KAAK;AACpD,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,QAAQ,GAAG,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC;AACvE,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC;AACnB,EAAE,OAAO,GAAG,GAAG,KAAK,IAAI,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,MAAM,GAAG,MAAM,EAAE;AACvF,IAAI,GAAG,IAAI,QAAQ,CAAC;AACpB,IAAI,QAAQ,IAAI,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACjF,CAAC,CAAC;AACF,MAAM,QAAQ,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,KAAK;AACrD,EAAE,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,GAAG;AACpC,IAAI,SAAS,CAAC,IAAI,CAAC;AACnB,IAAI,SAAS,CAAC,2BAA2B,CAAC,IAAI,CAAC,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,MAAM,qBAAqB,GAAG,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAC,gBAAgB,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1F,EAAE,IAAI,qBAAqB,IAAI,MAAM,EAAE;AACvC,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,EAAE,gBAAgB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACnE,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACtE,CAAC,CAAC;AACF,MAAM,uBAAuB,GAAG,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,GAAG,EAAE,KAAK;AACpG,EAAE,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAC5B,EAAE,IAAI,mBAAmB,IAAI,QAAQ,EAAE;AACvC,IAAI,mBAAmB,GAAG,QAAQ,GAAG,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,mBAAmB,IAAI,CAAC,EAAE;AAChC,IAAI,MAAM,IAAI,GAAG,GAAG,CAAC,mBAAmB,CAAC,CAAC;AAC1C,IAAI,iBAAiB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AAChD,GAAG;AACH,EAAE,MAAM,cAAc,GAAG,QAAQ,GAAG,mBAAmB,GAAG,CAAC,CAAC;AAC5D,EAAE,MAAM,oBAAoB,GAAG,cAAc,GAAG,kBAAkB,CAAC;AACnE,EAAE,OAAO,iBAAiB,GAAG,oBAAoB,CAAC;AAClD,CAAC,CAAC;AACF,MAAM,sBAAsB,GAAG,CAAC,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,KAAK;AAC9G,EAAE,IAAI,oBAAoB,GAAG,CAAC,CAAC;AAC/B,EAAE,IAAI,sBAAsB,GAAG,WAAW,EAAE;AAC5C,IAAI,sBAAsB,GAAG,WAAW,GAAG,CAAC,CAAC;AAC7C,GAAG;AACH,EAAE,IAAI,sBAAsB,IAAI,CAAC,EAAE;AACnC,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;AAChD,IAAI,oBAAoB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AACnD,GAAG;AACH,EAAE,MAAM,cAAc,GAAG,WAAW,GAAG,sBAAsB,GAAG,CAAC,CAAC;AAClE,EAAE,MAAM,oBAAoB,GAAG,cAAc,GAAG,oBAAoB,CAAC;AACrE,EAAE,OAAO,oBAAoB,GAAG,oBAAoB,CAAC;AACrD,CAAC,CAAC;AACF,MAAM,6BAA6B,GAAG;AACtC,EAAE,MAAM,EAAE,sBAAsB;AAChC,EAAE,GAAG,EAAE,uBAAuB;AAC9B,CAAC,CAAC;AACF,MAAM,SAAS,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc,KAAK;AAC1F,EAAE,MAAM,CAAC,IAAI,EAAE,uBAAuB,CAAC,GAAG;AAC1C,IAAI,IAAI,KAAK,KAAK,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AAC/C,IAAI,6BAA6B,CAAC,IAAI,CAAC;AACvC,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AAC3D,EAAE,MAAM,aAAa,GAAG,uBAAuB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC9D,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,aAAa,GAAG,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACnE,EAAE,MAAM,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5E,EAAE,IAAI,SAAS,KAAKA,wBAAe,EAAE;AACrC,IAAI,IAAI,YAAY,IAAI,SAAS,GAAG,IAAI,IAAI,YAAY,IAAI,SAAS,GAAG,IAAI,EAAE;AAC9E,MAAM,SAAS,GAAGC,uBAAc,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,SAAS,GAAGC,2BAAkB,CAAC;AACrC,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,SAAS;AACnB,IAAI,KAAKC,wBAAe,EAAE;AAC1B,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,KAAKC,sBAAa,EAAE;AACxB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,KAAKF,2BAAkB,EAAE;AAC7B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,KAAKD,uBAAc,CAAC;AACxB,IAAI,SAAS;AACb,MAAM,IAAI,YAAY,IAAI,SAAS,IAAI,YAAY,IAAI,SAAS,EAAE;AAClE,QAAQ,OAAO,YAAY,CAAC;AAC5B,OAAO,MAAM,IAAI,SAAS,GAAG,SAAS,EAAE;AACxC,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO,MAAM,IAAI,YAAY,GAAG,SAAS,EAAE;AAC3C,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO,MAAM;AACb,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACG,MAAC,eAAe,GAAGI,oBAAU,CAAC;AACnC,EAAE,IAAI,EAAE,mBAAmB;AAC3B,EAAE,iBAAiB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,KAAK;AAC5C,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/D,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,KAAK;AACzC,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,eAAe,EAAE,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,cAAc,KAAK,SAAS,CAAC,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,CAAC;AAC9K,EAAE,YAAY,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,KAAK,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,CAAC;AAChK,EAAE,4BAA4B,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC;AAC1G,EAAE,+BAA+B,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtE,IAAI,MAAM,SAAS,GAAG,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC;AAC/C,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC;AAC/B,IAAI,OAAO,SAAS,GAAG,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,MAAM,GAAG,SAAS,EAAE;AACpE,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC;AAC1E,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,uBAAuB;AACzB,EAAE,sBAAsB;AACxB,EAAE,yBAAyB,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC;AAClG,EAAE,4BAA4B,EAAE,CAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,KAAK;AACzE,IAAI,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACvC,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AACnE,IAAI,MAAM,SAAS,GAAG,SAAS,GAAG,MAAM,CAAC;AACzC,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AACzC,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC;AAC/B,IAAI,OAAO,SAAS,GAAG,QAAQ,GAAG,CAAC,IAAI,MAAM,GAAG,SAAS,EAAE;AAC3D,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC;AACtE,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,gBAAgB,EAAE,CAAC,QAAQ,EAAE,KAAK,KAAK;AACzC,IAAI,MAAM,UAAU,GAAG,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,EAAE,WAAW,KAAK;AACnE,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,WAAW,GAAGC,iBAAW,CAAC,WAAW,CAAC,GAAG,IAAI,GAAG,WAAW,CAAC;AAClE,MAAM,IAAIC,cAAQ,CAAC,WAAW,CAAC,EAAE;AACjC,QAAQ,KAAK,CAAC,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,sBAAsB,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC;AAC3G,OAAO;AACP,MAAM,IAAIA,cAAQ,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAQ,KAAK,CAAC,KAAK,CAAC,mBAAmB,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,mBAAmB,EAAE,QAAQ,GAAG,CAAC,CAAC,CAAC;AAClG,OAAO;AACP,MAAM,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5F,MAAM,IAAI,WAAW;AACrB,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACnE,KAAK,CAAC;AACN,IAAI,MAAM,qBAAqB,GAAG,CAAC,WAAW,EAAE,WAAW,KAAK;AAChE,MAAM,UAAU,CAAC;AACjB,QAAQ,WAAW;AACnB,OAAO,EAAE,WAAW,CAAC,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,MAAM,kBAAkB,GAAG,CAAC,QAAQ,EAAE,WAAW,KAAK;AAC1D,MAAM,UAAU,CAAC;AACjB,QAAQ,QAAQ;AAChB,OAAO,EAAE,WAAW,CAAC,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE;AAClC,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,MAAM,UAAU;AAChB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,EAAE,CAAC;AACd,IAAI,oBAAoB,GAAGC,uCAA8B;AACzD,IAAI,kBAAkB,GAAGA,uCAA8B;AACvD,GAAG,KAAK;AACR,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,oBAAoB;AAC1B,MAAM,kBAAkB;AACxB,MAAM,sBAAsB,EAAE,CAAC,CAAC;AAChC,MAAM,mBAAmB,EAAE,CAAC,CAAC;AAC7B,MAAM,GAAG,EAAE,EAAE;AACb,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,UAAU,EAAE,KAAK;AACnB,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK;AAejD,GAAG;AACH,CAAC;;;;"}