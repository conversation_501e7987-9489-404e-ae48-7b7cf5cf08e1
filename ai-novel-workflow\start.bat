@echo off
chcp 65001 >nul
echo ========================================
echo    AI小说生成工作流系统 - 启动脚本
echo ========================================
echo.

:: 检查Node.js是否安装
echo [1/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js，请先安装Node.js 16+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

:: 检查npm是否可用
echo [2/6] 检查npm环境...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm不可用
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

:: 检查是否首次运行
echo [3/6] 检查依赖安装状态...
if not exist "node_modules" (
    echo 📦 首次运行，正在安装依赖...
    call npm run install-all
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 检查环境变量文件
echo [4/6] 检查环境配置...
if not exist ".env" (
    echo 📝 创建环境配置文件...
    copy ".env.example" ".env" >nul
    echo ✅ 已创建.env文件，请根据需要修改配置
) else (
    echo ✅ 环境配置文件存在
)

:: 创建必要的目录
echo [5/6] 创建必要目录...
if not exist "database" mkdir database
if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

:: 启动服务
echo [6/6] 启动服务...
echo.
echo 🚀 正在启动AI小说生成工作流系统...
echo.
echo 前端地址: http://localhost:3000
echo 后端API: http://localhost:8080
echo.
echo 按 Ctrl+C 停止服务
echo.

:: 启动开发服务器
npm run dev

if %errorlevel% neq 0 (
    echo.
    echo ❌ 服务启动失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查端口3000和8080是否被占用
    echo 2. 检查防火墙设置
    echo 3. 重新安装依赖: npm run install-all
    echo.
    pause
    exit /b 1
)

pause
