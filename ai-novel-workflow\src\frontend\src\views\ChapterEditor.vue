<template>
  <div class="chapter-editor">
    <div class="editor-header">
      <div class="header-left">
        <el-button @click="goBack" type="text">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-input 
          v-model="chapter.title" 
          placeholder="章节标题"
          class="title-input"
          @blur="saveChapter"
        />
      </div>
      
      <div class="header-right">
        <span class="word-count">{{ wordCount }} 字</span>
        <el-button @click="generateContent" type="primary" :loading="generating">
          <el-icon><MagicStick /></el-icon>
          AI生成
        </el-button>
        <el-button @click="saveChapter" type="success" :loading="saving">
          <el-icon><Document /></el-icon>
          保存
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <div class="editor-sidebar">
        <el-card>
          <template #header>
            <span>章节大纲</span>
          </template>
          <el-input
            v-model="chapter.outline"
            type="textarea"
            :rows="8"
            placeholder="在这里写下章节大纲..."
            @blur="saveChapter"
          />
        </el-card>

        <el-card style="margin-top: 20px;">
          <template #header>
            <span>AI助手</span>
          </template>
          <div class="ai-tools">
            <el-button @click="generateOutline" size="small" style="width: 100%; margin-bottom: 10px;">
              生成大纲
            </el-button>
            <el-button @click="continueWriting" size="small" style="width: 100%; margin-bottom: 10px;">
              续写内容
            </el-button>
            <el-button @click="improveText" size="small" style="width: 100%;">
              优化文本
            </el-button>
          </div>
        </el-card>
      </div>

      <div class="editor-main">
        <div class="editor-toolbar">
          <el-button-group>
            <el-button @click="formatText('bold')" size="small">
              <strong>B</strong>
            </el-button>
            <el-button @click="formatText('italic')" size="small">
              <em>I</em>
            </el-button>
            <el-button @click="formatText('underline')" size="small">
              <u>U</u>
            </el-button>
          </el-button-group>
        </div>

        <div class="text-editor">
          <textarea
            ref="textareaRef"
            v-model="chapter.content"
            placeholder="开始写作..."
            @input="updateWordCount"
            @blur="saveChapter"
          ></textarea>
        </div>
      </div>
    </div>

    <!-- AI生成对话框 -->
    <el-dialog v-model="showGenerateDialog" title="AI内容生成" width="600px">
      <el-form :model="generateForm" label-width="100px">
        <el-form-item label="生成类型">
          <el-radio-group v-model="generateForm.type">
            <el-radio value="continue">续写内容</el-radio>
            <el-radio value="rewrite">重写段落</el-radio>
            <el-radio value="expand">扩展描述</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="生成长度">
          <el-slider v-model="generateForm.length" :min="100" :max="2000" show-input />
        </el-form-item>
        
        <el-form-item label="写作风格">
          <el-select v-model="generateForm.style" placeholder="选择风格">
            <el-option label="正常" value="normal" />
            <el-option label="生动" value="vivid" />
            <el-option label="简洁" value="concise" />
            <el-option label="华丽" value="elegant" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="提示词">
          <el-input 
            v-model="generateForm.prompt" 
            type="textarea" 
            :rows="3"
            placeholder="输入具体要求..."
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showGenerateDialog = false">取消</el-button>
        <el-button type="primary" @click="doGenerate" :loading="generating">
          生成
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, MagicStick, Document } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 响应式数据
const chapter = ref({
  id: null,
  title: '新章节',
  content: '',
  outline: '',
  projectId: null
})

const saving = ref(false)
const generating = ref(false)
const showGenerateDialog = ref(false)
const textareaRef = ref()

const generateForm = ref({
  type: 'continue',
  length: 500,
  style: 'normal',
  prompt: ''
})

// 计算字数
const wordCount = computed(() => {
  return chapter.value.content.length
})

// 更新字数
const updateWordCount = () => {
  // 字数会自动通过computed更新
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

// 保存章节
const saveChapter = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 500))
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 生成内容
const generateContent = () => {
  showGenerateDialog.value = true
}

// 执行生成
const doGenerate = async () => {
  generating.value = true
  try {
    // 模拟AI生成
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const sampleTexts = [
      '夜幕降临，城市的霓虹灯开始闪烁。主人公走在熟悉的街道上，心中涌起一阵莫名的感慨。',
      '远山如黛，近水含烟。在这个宁静的午后，一切都显得那么美好而安详。',
      '突然，一阵急促的脚步声打破了夜晚的宁静。他回头望去，只见一个黑影快速消失在巷子深处。'
    ]
    
    const randomText = sampleTexts[Math.floor(Math.random() * sampleTexts.length)]
    
    if (generateForm.value.type === 'continue') {
      chapter.value.content += '\n\n' + randomText
    } else {
      chapter.value.content = randomText
    }
    
    ElMessage.success('内容生成完成')
    showGenerateDialog.value = false
    
    // 自动保存
    await saveChapter()
    
  } catch (error) {
    ElMessage.error('生成失败')
  } finally {
    generating.value = false
  }
}

// 生成大纲
const generateOutline = async () => {
  generating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    chapter.value.outline = `1. 开场设定\n2. 人物登场\n3. 冲突发生\n4. 情节发展\n5. 章节结尾`
    ElMessage.success('大纲生成完成')
    await saveChapter()
  } finally {
    generating.value = false
  }
}

// 续写内容
const continueWriting = () => {
  generateForm.value.type = 'continue'
  generateContent()
}

// 优化文本
const improveText = async () => {
  if (!chapter.value.content.trim()) {
    ElMessage.warning('请先输入一些内容')
    return
  }
  
  generating.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    ElMessage.success('文本优化完成')
  } finally {
    generating.value = false
  }
}

// 格式化文本
const formatText = (format) => {
  const textarea = textareaRef.value
  if (!textarea) return
  
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = chapter.value.content.substring(start, end)
  
  if (!selectedText) {
    ElMessage.warning('请先选择要格式化的文本')
    return
  }
  
  let formattedText = selectedText
  switch (format) {
    case 'bold':
      formattedText = `**${selectedText}**`
      break
    case 'italic':
      formattedText = `*${selectedText}*`
      break
    case 'underline':
      formattedText = `<u>${selectedText}</u>`
      break
  }
  
  chapter.value.content = 
    chapter.value.content.substring(0, start) + 
    formattedText + 
    chapter.value.content.substring(end)
  
  nextTick(() => {
    textarea.focus()
    textarea.setSelectionRange(start, start + formattedText.length)
  })
}

// 组件挂载
onMounted(() => {
  const chapterId = route.params.id
  if (chapterId && chapterId !== 'new') {
    // 加载现有章节
    chapter.value.id = chapterId
    chapter.value.title = `第${chapterId}章`
    chapter.value.content = '这里是章节内容...'
  }
})
</script>

<style scoped>
.chapter-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.title-input {
  width: 300px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.word-count {
  color: #666;
  font-size: 14px;
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-sidebar {
  width: 300px;
  padding: 20px;
  border-right: 1px solid #e4e7ed;
  background: #f8f9fa;
  overflow-y: auto;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.editor-toolbar {
  padding: 10px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: white;
}

.text-editor {
  flex: 1;
  padding: 20px;
}

.text-editor textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  font-size: 16px;
  line-height: 1.8;
  font-family: 'Microsoft YaHei', sans-serif;
}

.ai-tools {
  display: flex;
  flex-direction: column;
}
</style>
