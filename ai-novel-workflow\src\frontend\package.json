{"name": "ai-novel-workflow-frontend", "version": "1.0.0", "description": "AI小说生成工作流系统 - 前端界面", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.2", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0"}, "keywords": ["vue", "frontend", "workflow", "ai", "novel"], "author": "AI Novel Workflow Team", "license": "MIT"}