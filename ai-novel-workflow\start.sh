#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "   AI小说生成工作流系统 - 启动脚本"
echo -e "========================================${NC}"
echo

# 检查Node.js是否安装
echo -e "${YELLOW}[1/6] 检查Node.js环境...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未检测到Node.js，请先安装Node.js 16+${NC}"
    echo "Ubuntu/Debian: sudo apt install nodejs npm"
    echo "CentOS/RHEL: sudo yum install nodejs npm"
    echo "macOS: brew install node"
    exit 1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js版本: $NODE_VERSION${NC}"

# 检查npm是否可用
echo -e "${YELLOW}[2/6] 检查npm环境...${NC}"
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ 错误: npm不可用${NC}"
    exit 1
fi

NPM_VERSION=$(npm --version)
echo -e "${GREEN}✅ npm版本: $NPM_VERSION${NC}"

# 检查是否首次运行
echo -e "${YELLOW}[3/6] 检查依赖安装状态...${NC}"
if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 首次运行，正在安装依赖...${NC}"
    npm run install-all
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 依赖安装失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 依赖已安装${NC}"
fi

# 检查环境变量文件
echo -e "${YELLOW}[4/6] 检查环境配置...${NC}"
if [ ! -f ".env" ]; then
    echo -e "${BLUE}📝 创建环境配置文件...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ 已创建.env文件，请根据需要修改配置${NC}"
else
    echo -e "${GREEN}✅ 环境配置文件存在${NC}"
fi

# 创建必要的目录
echo -e "${YELLOW}[5/6] 创建必要目录...${NC}"
mkdir -p database uploads logs
echo -e "${GREEN}✅ 目录创建完成${NC}"

# 检查端口是否被占用
echo -e "${YELLOW}[6/6] 检查端口占用...${NC}"
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  警告: 端口3000已被占用${NC}"
fi

if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  警告: 端口8080已被占用${NC}"
fi

# 启动服务
echo
echo -e "${GREEN}🚀 正在启动AI小说生成工作流系统...${NC}"
echo
echo -e "${BLUE}前端地址: http://localhost:3000${NC}"
echo -e "${BLUE}后端API: http://localhost:8080${NC}"
echo
echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"
echo

# 设置信号处理
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; exit 0' INT TERM

# 启动开发服务器
npm run dev

if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ 服务启动失败${NC}"
    echo
    echo "可能的解决方案:"
    echo "1. 检查端口3000和8080是否被占用"
    echo "2. 检查防火墙设置"
    echo "3. 重新安装依赖: npm run install-all"
    echo "4. 检查系统权限"
    echo
    exit 1
fi
