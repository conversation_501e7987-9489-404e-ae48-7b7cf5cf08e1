<template>
  <div class="settings">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <el-button @click="$router.push('/')" type="text">首页</el-button>
      <el-button @click="$router.push('/projects')" type="text">项目管理</el-button>
      <el-button @click="$router.push('/settings')" type="primary">系统设置</el-button>
    </div>

    <div class="page-header">
      <h1>⚙️ 系统设置</h1>
    </div>

    <el-tabs v-model="activeTab" class="settings-tabs">
      <!-- AI模型配置 -->
      <el-tab-pane label="AI模型配置" name="ai">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>AI模型设置</span>
              <el-button @click="testConnection" type="primary" :loading="testing">
                测试连接
              </el-button>
            </div>
          </template>

          <el-form :model="aiSettings" label-width="120px">
            <el-form-item label="模型类型">
              <el-radio-group v-model="aiSettings.modelType">
                <el-radio value="ollama">Ollama</el-radio>
                <el-radio value="lmstudio">LM Studio</el-radio>
                <el-radio value="localai">LocalAI</el-radio>
                <el-radio value="openai">OpenAI API</el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- Ollama配置 -->
            <template v-if="aiSettings.modelType === 'ollama'">
              <el-form-item label="服务地址">
                <el-input v-model="aiSettings.ollamaUrl" placeholder="http://localhost:11434" />
              </el-form-item>
              <el-form-item label="模型名称">
                <el-select v-model="aiSettings.ollamaModel" placeholder="选择模型">
                  <el-option label="llama2" value="llama2" />
                  <el-option label="llama3" value="llama3" />
                  <el-option label="qwen" value="qwen" />
                  <el-option label="chatglm" value="chatglm" />
                </el-select>
              </el-form-item>
            </template>

            <!-- LM Studio配置 -->
            <template v-if="aiSettings.modelType === 'lmstudio'">
              <el-form-item label="服务地址">
                <el-input v-model="aiSettings.lmstudioUrl" placeholder="http://localhost:1234" />
              </el-form-item>
              <el-form-item label="模型名称">
                <el-input v-model="aiSettings.lmstudioModel" placeholder="local-model" />
              </el-form-item>
            </template>

            <!-- OpenAI配置 -->
            <template v-if="aiSettings.modelType === 'openai'">
              <el-form-item label="API Key">
                <el-input v-model="aiSettings.openaiKey" type="password" placeholder="sk-..." />
              </el-form-item>
              <el-form-item label="模型">
                <el-select v-model="aiSettings.openaiModel">
                  <el-option label="GPT-3.5 Turbo" value="gpt-3.5-turbo" />
                  <el-option label="GPT-4" value="gpt-4" />
                  <el-option label="GPT-4 Turbo" value="gpt-4-turbo" />
                </el-select>
              </el-form-item>
            </template>

            <el-form-item label="最大Token数">
              <el-input-number v-model="aiSettings.maxTokens" :min="100" :max="4000" />
            </el-form-item>

            <el-form-item label="温度参数">
              <el-slider v-model="aiSettings.temperature" :min="0" :max="2" :step="0.1" show-input />
            </el-form-item>

            <el-form-item>
              <el-button @click="saveAISettings" type="primary" :loading="saving">
                保存设置
              </el-button>
              <el-button @click="resetAISettings">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 系统设置 -->
      <el-tab-pane label="系统设置" name="system">
        <el-card>
          <template #header>
            <span>系统配置</span>
          </template>

          <el-form :model="systemSettings" label-width="120px">
            <el-form-item label="主题">
              <el-radio-group v-model="systemSettings.theme">
                <el-radio value="light">浅色</el-radio>
                <el-radio value="dark">深色</el-radio>
                <el-radio value="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="语言">
              <el-select v-model="systemSettings.language">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>

            <el-form-item label="自动保存">
              <el-switch v-model="systemSettings.autoSave" />
              <span style="margin-left: 10px; color: #666;">
                每{{ systemSettings.autoSaveInterval }}秒自动保存
              </span>
            </el-form-item>

            <el-form-item label="保存间隔" v-if="systemSettings.autoSave">
              <el-input-number v-model="systemSettings.autoSaveInterval" :min="10" :max="300" />
              <span style="margin-left: 10px; color: #666;">秒</span>
            </el-form-item>

            <el-form-item>
              <el-button @click="saveSystemSettings" type="primary" :loading="saving">
                保存设置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 关于 -->
      <el-tab-pane label="关于" name="about">
        <el-card>
          <div class="about-content">
            <div class="logo">
              <h2>🤖 AI小说生成工作流系统</h2>
              <p class="version">版本 1.0.0</p>
            </div>

            <div class="info">
              <h3>系统信息</h3>
              <el-descriptions :column="1" border>
                <el-descriptions-item label="前端框架">Vue 3 + Element Plus</el-descriptions-item>
                <el-descriptions-item label="后端框架">Node.js + Express</el-descriptions-item>
                <el-descriptions-item label="数据库">SQLite</el-descriptions-item>
                <el-descriptions-item label="AI支持">Ollama, LM Studio, OpenAI</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="features">
              <h3>主要功能</h3>
              <ul>
                <li>📝 智能写作助手</li>
                <li>🔄 可视化工作流设计</li>
                <li>👥 角色管理系统</li>
                <li>📚 项目管理</li>
                <li>🎨 多种AI模型支持</li>
                <li>💾 自动保存与版本控制</li>
              </ul>
            </div>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const activeTab = ref('ai')
const saving = ref(false)
const testing = ref(false)

// AI设置
const aiSettings = ref({
  modelType: 'ollama',
  ollamaUrl: 'http://localhost:11434',
  ollamaModel: 'llama2',
  lmstudioUrl: 'http://localhost:1234',
  lmstudioModel: 'local-model',
  openaiKey: '',
  openaiModel: 'gpt-3.5-turbo',
  maxTokens: 2000,
  temperature: 0.7
})

// 系统设置
const systemSettings = ref({
  theme: 'light',
  language: 'zh-CN',
  autoSave: true,
  autoSaveInterval: 30
})

// 测试AI连接
const testConnection = async () => {
  testing.value = true
  try {
    // 模拟测试连接
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    if (aiSettings.value.modelType === 'ollama' && aiSettings.value.ollamaUrl) {
      ElMessage.success('Ollama连接测试成功！')
    } else if (aiSettings.value.modelType === 'openai' && aiSettings.value.openaiKey) {
      ElMessage.success('OpenAI连接测试成功！')
    } else {
      ElMessage.warning('请先完善配置信息')
    }
  } catch (error) {
    ElMessage.error('连接测试失败，请检查配置')
  } finally {
    testing.value = false
  }
}

// 保存AI设置
const saveAISettings = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('AI设置保存成功！')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 重置AI设置
const resetAISettings = () => {
  aiSettings.value = {
    modelType: 'ollama',
    ollamaUrl: 'http://localhost:11434',
    ollamaModel: 'llama2',
    lmstudioUrl: 'http://localhost:1234',
    lmstudioModel: 'local-model',
    openaiKey: '',
    openaiModel: 'gpt-3.5-turbo',
    maxTokens: 2000,
    temperature: 0.7
  }
  ElMessage.info('设置已重置')
}

// 保存系统设置
const saveSystemSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('系统设置保存成功！')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    saving.value = false
  }
}

// 加载设置
const loadSettings = () => {
  // 从localStorage加载设置
  const savedAI = localStorage.getItem('aiSettings')
  const savedSystem = localStorage.getItem('systemSettings')
  
  if (savedAI) {
    aiSettings.value = { ...aiSettings.value, ...JSON.parse(savedAI) }
  }
  
  if (savedSystem) {
    systemSettings.value = { ...systemSettings.value, ...JSON.parse(savedSystem) }
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.settings {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.nav-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.page-header h1 {
  margin: 0 0 30px 0;
  color: #333;
}

.settings-tabs {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.about-content {
  text-align: center;
}

.logo h2 {
  margin: 0 0 10px 0;
  color: #409eff;
}

.version {
  color: #666;
  margin-bottom: 30px;
}

.info, .features {
  margin: 30px 0;
  text-align: left;
}

.features ul {
  list-style: none;
  padding: 0;
}

.features li {
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.features li:last-child {
  border-bottom: none;
}
</style>
