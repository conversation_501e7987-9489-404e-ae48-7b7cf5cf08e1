<template>
  <div class="workflow-designer">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-dropdown @command="handleSaveCommand">
          <el-button type="primary">
            <el-icon><Document /></el-icon>
            保存工作流
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="save">保存到本地存储</el-dropdown-item>
              <el-dropdown-item command="export">导出为文件</el-dropdown-item>
              <el-dropdown-item command="saveAs">另存为...</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-dropdown @command="handleLoadCommand">
          <el-button type="info">
            <el-icon><FolderOpened /></el-icon>
            加载工作流
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="load">从本地存储加载</el-dropdown-item>
              <el-dropdown-item command="import">导入文件</el-dropdown-item>
              <el-dropdown-item command="manage">管理已保存的工作流</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button @click="runWorkflow" type="success">
          <el-icon><VideoPlay /></el-icon>
          运行
        </el-button>
        <el-button @click="clearCanvas">
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
      
      <div class="toolbar-center">
        <el-button @click="showApiSettings" type="info">
          <el-icon><Setting /></el-icon>
          API配置
        </el-button>
      </div>
      
      <div class="toolbar-right">
        <el-button @click="zoomIn">放大</el-button>
        <el-button @click="zoomOut">缩小</el-button>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>

    <div class="designer-content">
      <!-- 左侧节点面板 -->
      <div class="node-panel">
        <div class="panel-header">
          <div class="panel-title">
            <el-icon class="title-icon"><Grid /></el-icon>
            <span>节点库</span>
          </div>
          <el-input
            v-model="nodeSearchKeyword"
            placeholder="搜索节点..."
            size="small"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="panel-content">
          <el-collapse v-model="activeCategories" class="node-categories">
            <!-- 输入节点 -->
            <el-collapse-item name="input" class="category-item">
              <template #title>
                <div class="category-header">
                  <div class="category-icon input-category">
                    <el-icon><Upload /></el-icon>
                  </div>
                  <span class="category-title">输入节点</span>
                  <el-tag size="small" class="node-count">{{ filteredInputNodes.length }}</el-tag>
                </div>
              </template>
              <div class="node-grid">
                <div
                  class="node-card"
                  v-for="node in filteredInputNodes"
                  :key="node.type"
                  @dragstart="onDragStart($event, node)"
                  draggable="true"
                >
                  <div class="node-card-header">
                    <div class="node-icon" :style="{ background: node.color }">
                      {{ node.icon }}
                    </div>
                    <div class="node-badge">输入</div>
                  </div>
                  <div class="node-card-body">
                    <h4 class="node-title">{{ node.name }}</h4>
                    <p class="node-description">{{ node.description }}</p>
                  </div>
                  <div class="node-card-footer">
                    <div class="node-ports">
                      <span class="port-info">
                        <el-icon><Right /></el-icon>
                        {{ node.outputs }} 输出
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>

            <!-- AI处理节点 -->
            <el-collapse-item name="ai" class="category-item">
              <template #title>
                <div class="category-header">
                  <div class="category-icon ai-category">
                    <el-icon><Cpu /></el-icon>
                  </div>
                  <span class="category-title">AI处理</span>
                  <el-tag size="small" class="node-count">{{ filteredAiNodes.length }}</el-tag>
                </div>
              </template>
              <div class="node-grid">
                <div
                  class="node-card"
                  v-for="node in filteredAiNodes"
                  :key="node.type"
                  @dragstart="onDragStart($event, node)"
                  draggable="true"
                >
                  <div class="node-card-header">
                    <div class="node-icon" :style="{ background: node.color }">
                      {{ node.icon }}
                    </div>
                    <div class="node-badge ai-badge">AI</div>
                  </div>
                  <div class="node-card-body">
                    <h4 class="node-title">{{ node.name }}</h4>
                    <p class="node-description">{{ node.description }}</p>
                  </div>
                  <div class="node-card-footer">
                    <div class="node-ports">
                      <span class="port-info">
                        <el-icon><Right /></el-icon>
                        {{ node.inputs }} 输入 · {{ node.outputs }} 输出
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>

            <!-- 输出节点 -->
            <el-collapse-item name="output" class="category-item">
              <template #title>
                <div class="category-header">
                  <div class="category-icon output-category">
                    <el-icon><Download /></el-icon>
                  </div>
                  <span class="category-title">输出节点</span>
                  <el-tag size="small" class="node-count">{{ filteredOutputNodes.length }}</el-tag>
                </div>
              </template>
              <div class="node-grid">
                <div
                  class="node-card"
                  v-for="node in filteredOutputNodes"
                  :key="node.type"
                  @dragstart="onDragStart($event, node)"
                  draggable="true"
                >
                  <div class="node-card-header">
                    <div class="node-icon" :style="{ background: node.color }">
                      {{ node.icon }}
                    </div>
                    <div class="node-badge output-badge">输出</div>
                  </div>
                  <div class="node-card-body">
                    <h4 class="node-title">{{ node.name }}</h4>
                    <p class="node-description">{{ node.description }}</p>
                  </div>
                  <div class="node-card-footer">
                    <div class="node-ports">
                      <span class="port-info">
                        <el-icon><Right /></el-icon>
                        {{ node.inputs }} 输入
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="canvas-container">
        <div 
          class="canvas" 
          ref="canvasRef"
          @drop="onDrop"
          @dragover.prevent
          @click="clearSelection"
          :style="{ transform: `scale(${zoomLevel})` }"
        >
          <!-- 网格背景 -->
          <div class="grid-background"></div>
          
          <!-- 连接线 -->
          <svg class="connections" :style="{ transform: `scale(${1/zoomLevel})` }">
            <path
              v-for="connection in connections"
              :key="`${connection.from}-${connection.to}`"
              :d="getConnectionPath(connection)"
              stroke="#409eff"
              stroke-width="2"
              fill="none"
              marker-end="url(#arrowhead)"
            />
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" 
                      refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#409eff" />
              </marker>
            </defs>
          </svg>
          
          <!-- 工作流节点 -->
          <div
            v-for="node in workflowNodes"
            :key="node.id"
            class="workflow-node"
            :class="{ 
              'selected': selectedNode === node.id,
              'running': runningNodes.includes(node.id)
            }"
            :style="{ 
              left: node.x + 'px', 
              top: node.y + 'px',
              background: node.color 
            }"
            @click.stop="selectNode(node.id)"
            @mousedown="startDrag(node.id, $event)"
          >
            <div class="node-header">
              <span class="node-icon">{{ node.icon }}</span>
              <span class="node-title">{{ node.name }}</span>
              <el-button 
                class="delete-btn" 
                size="small" 
                type="danger" 
                @click.stop="deleteNode(node.id)"
              >
                ×
              </el-button>
            </div>
            
            <!-- 输入连接点 -->
            <div 
              v-if="node.inputs > 0"
              class="input-port"
              @click.stop="connectPort(node.id, 'input')"
            ></div>
            
            <!-- 输出连接点 -->
            <div 
              v-if="node.outputs > 0"
              class="output-port"
              @click.stop="connectPort(node.id, 'output')"
            ></div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="property-panel">
        <div class="panel-section">
          <h3>节点属性</h3>
          <div v-if="selectedNodeData">
            <el-form :model="selectedNodeData" label-width="80px">
              <el-form-item label="节点名称">
                <el-input v-model="selectedNodeData.name" />
              </el-form-item>
              
              <el-form-item label="描述">
                <el-input 
                  v-model="selectedNodeData.description" 
                  type="textarea" 
                  :rows="3"
                />
              </el-form-item>
              
              <!-- AI节点特殊配置 -->
              <template v-if="selectedNodeData.type.includes('ai')">
                <el-form-item label="模型">
                  <el-select v-model="selectedNodeData.model">
                    <el-option label="GPT-3.5" value="gpt-3.5" />
                    <el-option label="GPT-4" value="gpt-4" />
                    <el-option label="Llama2" value="llama2" />
                  </el-select>
                </el-form-item>
                
                <el-form-item label="提示词">
                  <el-input 
                    v-model="selectedNodeData.prompt" 
                    type="textarea" 
                    :rows="4"
                    placeholder="输入AI提示词..."
                  />
                </el-form-item>
              </template>
            </el-form>
          </div>
          <div v-else class="no-selection">
            <p>请选择一个节点来编辑属性</p>
          </div>
        </div>
      </div>
    </div>

    <!-- API配置对话框 -->
    <el-dialog
      v-model="apiDialogVisible"
      title="API配置"
      width="600px"
      :before-close="handleApiDialogClose"
    >
      <el-form :model="apiConfig" label-width="120px">

        <div class="custom-apis">
          <div class="custom-api-header">
            <span>自定义API配置 - 支持OpenAI、Claude、Gemini等各种AI服务</span>
            <el-button type="primary" size="small" @click="addCustomApi">
              <el-icon><Plus /></el-icon>
              添加API配置
            </el-button>
          </div>

          <div
            v-for="(customApi, index) in apiConfig.customApis"
            :key="index"
            class="custom-api-item"
          >
            <el-card>
              <template #header>
                <div class="custom-api-title">
                  <span>自定义API #{{ index + 1 }}</span>
                  <el-button
                    type="danger"
                    size="small"
                    @click="removeCustomApi(index)"
                  >
                    删除
                  </el-button>
                </div>
              </template>

              <el-form :model="customApi" label-width="100px" size="small">
                <el-form-item label="API名称">
                  <el-input v-model="customApi.name" placeholder="例如: DeepSeek" />
                </el-form-item>

                <el-form-item label="API密钥">
                  <el-input
                    v-model="customApi.apiKey"
                    type="password"
                    placeholder="请输入API密钥"
                    show-password
                  />
                </el-form-item>

                <el-form-item label="API地址">
                  <el-input
                    v-model="customApi.baseUrl"
                    placeholder="https://api.example.com/v1"
                  />
                </el-form-item>

                <el-form-item label="可用模型">
                  <div class="model-tags">
                    <el-tag
                      v-for="model in customApi.models"
                      :key="model"
                      closable
                      @close="removeModel(customApi, model)"
                      class="model-tag"
                    >
                      {{ model }}
                    </el-tag>
                    <el-input
                      v-if="customApi.inputVisible"
                      ref="modelInputRef"
                      v-model="customApi.inputValue"
                      size="small"
                      @keyup.enter="confirmModel(customApi)"
                      @blur="confirmModel(customApi)"
                      class="model-input"
                    />
                    <el-button
                      v-else
                      size="small"
                      @click="showModelInput(customApi)"
                    >
                      + 添加模型
                    </el-button>
                  </div>
                </el-form-item>

                <el-form-item label="最大Token">
                  <div class="token-control">
                    <el-checkbox
                      v-model="customApi.unlimitedTokens"
                      @change="handleTokenLimitChange(customApi)"
                    >
                      无限制
                    </el-checkbox>
                    <el-input-number
                      v-model="customApi.maxTokens"
                      :min="1000"
                      :max="10000000"
                      :step="1000"
                      :disabled="customApi.unlimitedTokens"
                      style="margin-left: 12px; flex: 1"
                    />
                  </div>
                  <div class="token-presets">
                    <el-button-group size="small">
                      <el-button
                        @click="setTokenPreset(customApi, 4000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        4K
                      </el-button>
                      <el-button
                        @click="setTokenPreset(customApi, 8000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        8K
                      </el-button>
                      <el-button
                        @click="setTokenPreset(customApi, 16000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        16K
                      </el-button>
                      <el-button
                        @click="setTokenPreset(customApi, 32000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        32K
                      </el-button>
                      <el-button
                        @click="setTokenPreset(customApi, 128000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        128K
                      </el-button>
                      <el-button
                        @click="setTokenPreset(customApi, 1000000)"
                        :disabled="customApi.unlimitedTokens"
                      >
                        1M
                      </el-button>
                    </el-button-group>
                  </div>
                </el-form-item>

                <el-form-item label="创造性">
                  <el-slider
                    v-model="customApi.temperature"
                    :min="0"
                    :max="2"
                    :step="0.1"
                    show-input
                  />
                </el-form-item>
              </el-form>
            </el-card>
          </div>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="testApiConnection" :loading="testingApi">
            测试连接
          </el-button>
          <el-button @click="apiDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveApiConfig">
            保存配置
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 工作流管理对话框 -->
    <el-dialog
      v-model="workflowManagerVisible"
      title="工作流管理"
      width="800px"
    >
      <div class="workflow-manager">
        <div class="manager-header">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索工作流..."
            style="width: 300px"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div class="manager-actions">
            <el-button type="primary" @click="showSaveAsDialog">
              <el-icon><Plus /></el-icon>
              新建工作流
            </el-button>
          </div>
        </div>

        <el-table :data="filteredWorkflows" style="width: 100%">
          <el-table-column prop="name" label="工作流名称" width="200" />
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="nodeCount" label="节点数" width="80" />
          <el-table-column prop="updateTime" label="更新时间" width="150" />
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button size="small" @click="loadWorkflowById(scope.row.id)">
                加载
              </el-button>
              <el-button size="small" @click="exportWorkflowById(scope.row.id)">
                导出
              </el-button>
              <el-button
                size="small"
                type="danger"
                @click="deleteWorkflowById(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 另存为对话框 -->
    <el-dialog
      v-model="saveAsDialogVisible"
      title="保存工作流"
      width="500px"
    >
      <el-form :model="saveAsForm" label-width="80px">
        <el-form-item label="名称" required>
          <el-input v-model="saveAsForm.name" placeholder="请输入工作流名称" />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="saveAsForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入工作流描述"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="saveAsForm.tags" placeholder="用逗号分隔多个标签" />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="saveAsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveWorkflowAs">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".json,.workflow"
      @change="handleFileImport"
      style="display: none"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, VideoPlay, Delete, Setting, Plus, ArrowDown, FolderOpened, Search, Grid, Upload, Download, Cpu, Right } from '@element-plus/icons-vue'

// 响应式数据
const canvasRef = ref()
const workflowNodes = ref([])
const connections = ref([])
const selectedNode = ref(null)
const zoomLevel = ref(1)
const draggedNode = ref(null)
const connectingFrom = ref(null)
const runningNodes = ref([])

// API配置相关
const apiDialogVisible = ref(false)
const testingApi = ref(false)
const apiConfig = ref({
  customApis: []
})

// 工作流管理相关
const workflowManagerVisible = ref(false)
const saveAsDialogVisible = ref(false)
const searchKeyword = ref('')
const fileInputRef = ref()
const savedWorkflows = ref([])
const saveAsForm = ref({
  name: '',
  description: '',
  tags: ''
})

// 节点面板相关
const nodeSearchKeyword = ref('')
const activeCategories = ref(['input', 'ai', 'output'])

// 节点类型定义
const inputNodes = ref([
  { type: 'text-input', name: '文本输入', icon: '📝', color: '#67C23A', inputs: 0, outputs: 1, description: '手动输入文本内容，支持多行文本编辑' },
  { type: 'file-input', name: '文件输入', icon: '📁', color: '#67C23A', inputs: 0, outputs: 1, description: '从本地文件读取内容，支持多种格式' },
  { type: 'user-input', name: '用户输入', icon: '👤', color: '#67C23A', inputs: 0, outputs: 1, description: '运行时提示用户输入内容' }
])

const aiNodes = ref([
  { type: 'ai-generate', name: 'AI生成', icon: '🤖', color: '#409EFF', inputs: 1, outputs: 1, description: '使用AI模型生成新的文本内容' },
  { type: 'ai-rewrite', name: 'AI改写', icon: '✏️', color: '#E6A23C', inputs: 1, outputs: 1, description: '使用AI重写和优化现有文本' },
  { type: 'ai-analyze', name: 'AI分析', icon: '🔍', color: '#F56C6C', inputs: 1, outputs: 1, description: '分析文本内容并提取关键信息' },
  { type: 'ai-translate', name: 'AI翻译', icon: '🌐', color: '#909399', inputs: 1, outputs: 1, description: '将文本翻译成其他语言' },
  { type: 'character-gen', name: '角色生成', icon: '👥', color: '#9C27B0', inputs: 1, outputs: 1, description: '生成小说角色设定和背景故事' }
])

const outputNodes = ref([
  { type: 'text-output', name: '文本输出', icon: '📄', color: '#67C23A', inputs: 1, outputs: 0, description: '将处理结果输出为文本格式' },
  { type: 'file-output', name: '文件输出', icon: '💾', color: '#67C23A', inputs: 1, outputs: 0, description: '将结果保存为本地文件' },
  { type: 'preview', name: '预览', icon: '👁️', color: '#409EFF', inputs: 1, outputs: 0, description: '实时预览处理结果' }
])

// 计算属性
const selectedNodeData = computed(() => {
  if (!selectedNode.value) return null
  return workflowNodes.value.find(node => node.id === selectedNode.value)
})

const filteredWorkflows = computed(() => {
  if (!searchKeyword.value) return savedWorkflows.value
  return savedWorkflows.value.filter(workflow =>
    workflow.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    workflow.description.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 节点过滤计算属性
const filteredInputNodes = computed(() => {
  if (!nodeSearchKeyword.value) return inputNodes.value
  return inputNodes.value.filter(node =>
    node.name.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase())
  )
})

const filteredAiNodes = computed(() => {
  if (!nodeSearchKeyword.value) return aiNodes.value
  return aiNodes.value.filter(node =>
    node.name.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase())
  )
})

const filteredOutputNodes = computed(() => {
  if (!nodeSearchKeyword.value) return outputNodes.value
  return outputNodes.value.filter(node =>
    node.name.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase()) ||
    node.description.toLowerCase().includes(nodeSearchKeyword.value.toLowerCase())
  )
})

// 拖拽开始
const onDragStart = (event, nodeType) => {
  draggedNode.value = nodeType
}

// 放置节点
const onDrop = (event) => {
  if (!draggedNode.value) return
  
  const rect = canvasRef.value.getBoundingClientRect()
  const x = (event.clientX - rect.left) / zoomLevel.value - 75
  const y = (event.clientY - rect.top) / zoomLevel.value - 40
  
  const newNode = {
    id: Date.now().toString(),
    ...draggedNode.value,
    x: Math.max(0, x),
    y: Math.max(0, y),
    model: 'gpt-3.5',
    prompt: '',
    description: ''
  }
  
  workflowNodes.value.push(newNode)
  draggedNode.value = null
}

// 选择节点
const selectNode = (nodeId) => {
  selectedNode.value = nodeId
}

// 清除选择
const clearSelection = () => {
  selectedNode.value = null
  connectingFrom.value = null
}

// 删除节点
const deleteNode = (nodeId) => {
  workflowNodes.value = workflowNodes.value.filter(node => node.id !== nodeId)
  connections.value = connections.value.filter(
    conn => conn.from !== nodeId && conn.to !== nodeId
  )
  if (selectedNode.value === nodeId) {
    selectedNode.value = null
  }
}

// 连接端口
const connectPort = (nodeId, portType) => {
  if (!connectingFrom.value) {
    if (portType === 'output') {
      connectingFrom.value = nodeId
    }
  } else {
    if (portType === 'input' && connectingFrom.value !== nodeId) {
      // 检查是否已存在连接
      const existingConnection = connections.value.find(
        conn => conn.from === connectingFrom.value && conn.to === nodeId
      )
      
      if (!existingConnection) {
        connections.value.push({
          from: connectingFrom.value,
          to: nodeId
        })
      }
    }
    connectingFrom.value = null
  }
}

// 获取连接路径
const getConnectionPath = (connection) => {
  const fromNode = workflowNodes.value.find(n => n.id === connection.from)
  const toNode = workflowNodes.value.find(n => n.id === connection.to)
  
  if (!fromNode || !toNode) return ''
  
  const fromX = fromNode.x + 150
  const fromY = fromNode.y + 40
  const toX = toNode.x
  const toY = toNode.y + 40
  
  const midX = (fromX + toX) / 2
  
  return `M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`
}

// 缩放控制
const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

// 清空画布
const clearCanvas = () => {
  workflowNodes.value = []
  connections.value = []
  selectedNode.value = null
}

// 工作流保存和加载相关方法
const handleSaveCommand = (command) => {
  switch (command) {
    case 'save':
      saveToLocalStorage()
      break
    case 'export':
      exportWorkflow()
      break
    case 'saveAs':
      showSaveAsDialog()
      break
  }
}

const handleLoadCommand = (command) => {
  switch (command) {
    case 'load':
      loadFromLocalStorage()
      break
    case 'import':
      importWorkflow()
      break
    case 'manage':
      showWorkflowManager()
      break
  }
}

const saveToLocalStorage = () => {
  const workflow = {
    nodes: workflowNodes.value,
    connections: connections.value,
    timestamp: Date.now()
  }
  localStorage.setItem('currentWorkflow', JSON.stringify(workflow))
  ElMessage.success('工作流已保存到本地存储')
}

const loadFromLocalStorage = () => {
  const saved = localStorage.getItem('currentWorkflow')
  if (saved) {
    const workflow = JSON.parse(saved)
    workflowNodes.value = workflow.nodes || []
    connections.value = workflow.connections || []
    ElMessage.success('工作流已从本地存储加载')
  } else {
    ElMessage.warning('没有找到保存的工作流')
  }
}

const exportWorkflow = () => {
  const workflow = {
    name: '工作流导出',
    description: '从工作流设计器导出',
    nodes: workflowNodes.value,
    connections: connections.value,
    exportTime: new Date().toISOString(),
    version: '1.0'
  }

  const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `workflow_${Date.now()}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)

  ElMessage.success('工作流已导出')
}

const importWorkflow = () => {
  fileInputRef.value?.click()
}

const handleFileImport = (event) => {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const workflow = JSON.parse(e.target.result)
      if (workflow.nodes && workflow.connections) {
        workflowNodes.value = workflow.nodes
        connections.value = workflow.connections
        ElMessage.success('工作流导入成功')
      } else {
        ElMessage.error('无效的工作流文件格式')
      }
    } catch (error) {
      ElMessage.error('工作流文件解析失败')
    }
  }
  reader.readAsText(file)

  // 清空文件输入
  event.target.value = ''
}

// 运行工作流
const runWorkflow = async () => {
  if (workflowNodes.value.length === 0) {
    ElMessage.warning('请先添加节点')
    return
  }
  
  ElMessage.info('开始运行工作流...')
  
  // 模拟运行过程
  for (const node of workflowNodes.value) {
    runningNodes.value.push(node.id)
    await new Promise(resolve => setTimeout(resolve, 1000))
    runningNodes.value = runningNodes.value.filter(id => id !== node.id)
  }
  
  ElMessage.success('工作流运行完成')
}

// 节点拖拽
let isDragging = false
let dragOffset = { x: 0, y: 0 }

const startDrag = (nodeId, event) => {
  const node = workflowNodes.value.find(n => n.id === nodeId)
  if (!node) return
  
  isDragging = true
  dragOffset.x = event.clientX - node.x * zoomLevel.value
  dragOffset.y = event.clientY - node.y * zoomLevel.value
  
  const onMouseMove = (e) => {
    if (!isDragging) return
    node.x = (e.clientX - dragOffset.x) / zoomLevel.value
    node.y = (e.clientY - dragOffset.y) / zoomLevel.value
  }
  
  const onMouseUp = () => {
    isDragging = false
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
  }
  
  document.addEventListener('mousemove', onMouseMove)
  document.addEventListener('mouseup', onMouseUp)
}

// API配置相关方法
const showApiSettings = () => {
  apiDialogVisible.value = true
}

const saveApiConfig = () => {
  localStorage.setItem('apiConfig', JSON.stringify(apiConfig.value))
  ElMessage.success('API配置已保存')
  apiDialogVisible.value = false
}

const testApiConnection = async () => {
  testingApi.value = true
  try {
    // 这里可以添加实际的API测试逻辑
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('API连接测试成功')
  } catch (error) {
    ElMessage.error('API连接测试失败')
  } finally {
    testingApi.value = false
  }
}

const handleApiDialogClose = (done) => {
  done()
}

// 自定义API相关方法
const addCustomApi = () => {
  const apiCount = apiConfig.value.customApis.length + 1
  apiConfig.value.customApis.push({
    name: `API配置 ${apiCount}`,
    apiKey: '',
    baseUrl: 'https://api.openai.com/v1',
    models: ['gpt-3.5-turbo'],
    maxTokens: 4000,
    unlimitedTokens: false,
    temperature: 0.7,
    inputVisible: false,
    inputValue: ''
  })
}

const removeCustomApi = (index) => {
  apiConfig.value.customApis.splice(index, 1)
}

const showModelInput = (customApi) => {
  customApi.inputVisible = true
  nextTick(() => {
    // 聚焦到输入框 - 可以在这里添加聚焦逻辑
  })
}

const confirmModel = (customApi) => {
  if (customApi.inputValue && !customApi.models.includes(customApi.inputValue)) {
    customApi.models.push(customApi.inputValue)
  }
  customApi.inputVisible = false
  customApi.inputValue = ''
}

const removeModel = (customApi, model) => {
  const index = customApi.models.indexOf(model)
  if (index > -1) {
    customApi.models.splice(index, 1)
  }
}

// Token控制相关方法
const handleTokenLimitChange = (customApi) => {
  if (customApi.unlimitedTokens) {
    customApi.maxTokens = null
  } else {
    customApi.maxTokens = 4000
  }
}

const setTokenPreset = (customApi, tokens) => {
  if (!customApi.unlimitedTokens) {
    customApi.maxTokens = tokens
  }
}

// 工作流管理器相关方法
const showWorkflowManager = () => {
  loadSavedWorkflows()
  workflowManagerVisible.value = true
}

const showSaveAsDialog = () => {
  saveAsForm.value = {
    name: '',
    description: '',
    tags: ''
  }
  saveAsDialogVisible.value = true
}

const saveWorkflowAs = () => {
  if (!saveAsForm.value.name.trim()) {
    ElMessage.warning('请输入工作流名称')
    return
  }

  const workflow = {
    id: Date.now().toString(),
    name: saveAsForm.value.name,
    description: saveAsForm.value.description,
    tags: saveAsForm.value.tags.split(',').map(tag => tag.trim()).filter(Boolean),
    nodes: workflowNodes.value,
    connections: connections.value,
    nodeCount: workflowNodes.value.length,
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }

  const saved = JSON.parse(localStorage.getItem('savedWorkflows') || '[]')
  saved.push(workflow)
  localStorage.setItem('savedWorkflows', JSON.stringify(saved))

  ElMessage.success('工作流保存成功')
  saveAsDialogVisible.value = false
  loadSavedWorkflows()
}

const loadSavedWorkflows = () => {
  const saved = JSON.parse(localStorage.getItem('savedWorkflows') || '[]')
  savedWorkflows.value = saved.map(workflow => ({
    ...workflow,
    updateTime: new Date(workflow.updateTime).toLocaleString()
  }))
}

const loadWorkflowById = (id) => {
  const saved = JSON.parse(localStorage.getItem('savedWorkflows') || '[]')
  const workflow = saved.find(w => w.id === id)
  if (workflow) {
    workflowNodes.value = workflow.nodes || []
    connections.value = workflow.connections || []
    ElMessage.success(`已加载工作流: ${workflow.name}`)
    workflowManagerVisible.value = false
  }
}

const exportWorkflowById = (id) => {
  const saved = JSON.parse(localStorage.getItem('savedWorkflows') || '[]')
  const workflow = saved.find(w => w.id === id)
  if (workflow) {
    const blob = new Blob([JSON.stringify(workflow, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${workflow.name}_${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    ElMessage.success('工作流已导出')
  }
}

const deleteWorkflowById = (id) => {
  const saved = JSON.parse(localStorage.getItem('savedWorkflows') || '[]')
  const index = saved.findIndex(w => w.id === id)
  if (index > -1) {
    saved.splice(index, 1)
    localStorage.setItem('savedWorkflows', JSON.stringify(saved))
    loadSavedWorkflows()
    ElMessage.success('工作流已删除')
  }
}

// 组件挂载时加载保存的工作流和API配置
onMounted(() => {
  // 加载当前工作流
  const saved = localStorage.getItem('currentWorkflow')
  if (saved) {
    const workflow = JSON.parse(saved)
    workflowNodes.value = workflow.nodes || []
    connections.value = workflow.connections || []
  }

  // 加载API配置
  const savedApiConfig = localStorage.getItem('apiConfig')
  if (savedApiConfig) {
    apiConfig.value = { ...apiConfig.value, ...JSON.parse(savedApiConfig) }
  }

  // 加载已保存的工作流列表
  loadSavedWorkflows()
})
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #1a1a2e;
  color: white;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background: #16213e;
  border-bottom: 1px solid #0f3460;
}

.workflow-title {
  color: #fff;
  font-weight: bold;
}

.zoom-level {
  margin-left: 10px;
  color: #ccc;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-panel {
  width: 320px;
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color-page);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.title-icon {
  color: var(--el-color-primary);
}

.search-input {
  width: 100%;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.node-categories {
  border: none;
}

.category-item {
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 8px;
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 4px 0;
}

.category-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.input-category {
  background: linear-gradient(135deg, #67C23A, #85CE61);
}

.ai-category {
  background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.output-category {
  background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.category-title {
  flex: 1;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.node-count {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: none;
}

.node-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  padding: 16px;
}

.node-card {
  background: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 12px;
  padding: 16px;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.node-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.node-card:active {
  cursor: grabbing;
  transform: translateY(0);
}

.node-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.node-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.node-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.node-badge {
  background: var(--el-color-success-light-9);
  color: var(--el-color-success);
}

.ai-badge {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.output-badge {
  background: var(--el-color-warning-light-9);
  color: var(--el-color-warning);
}

.node-card-body {
  margin-bottom: 12px;
}

.node-title {
  margin: 0 0 6px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.node-description {
  margin: 0;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  line-height: 1.4;
}

.node-card-footer {
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 8px;
}

.node-ports {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.port-info {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: auto;
  background: #1a1a2e;
}

.canvas {
  position: relative;
  width: 2000px;
  height: 2000px;
  transform-origin: 0 0;
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.workflow-node {
  position: absolute;
  width: 150px;
  min-height: 80px;
  border-radius: 8px;
  border: 2px solid transparent;
  cursor: move;
  z-index: 2;
  transition: all 0.2s;
}

.workflow-node.selected {
  border-color: #409eff;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.5);
}

.workflow-node.running {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.5); }
  50% { box-shadow: 0 0 20px rgba(64, 158, 255, 0.8); }
  100% { box-shadow: 0 0 5px rgba(64, 158, 255, 0.5); }
}

.node-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background: rgba(0,0,0,0.2);
  border-radius: 6px 6px 0 0;
  position: relative;
}

.node-header .node-icon {
  margin-right: 8px;
}

.node-title {
  flex: 1;
  font-size: 12px;
  font-weight: bold;
}

.delete-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  padding: 0;
  font-size: 12px;
}

.input-port, .output-port {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #409eff;
  border: 2px solid white;
  cursor: pointer;
}

.input-port {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.output-port {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.property-panel {
  width: 300px;
  background: #16213e;
  border-left: 1px solid #0f3460;
  overflow-y: auto;
}

.no-selection {
  text-align: center;
  color: #ccc;
  padding: 40px 20px;
}

/* API配置对话框样式 */
.custom-apis {
  margin-top: 20px;
}

.custom-api-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--el-color-warning-light-9);
  border-radius: 6px;
  border-left: 4px solid var(--el-color-warning);
}

.custom-api-header span {
  color: var(--el-color-warning-dark-2);
  font-size: 14px;
}

.custom-api-item {
  margin-bottom: 16px;
}

.custom-api-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.model-tag {
  margin: 0;
}

.model-input {
  width: 120px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 工作流管理器样式 */
.workflow-manager {
  padding: 0;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
}

.manager-actions {
  display: flex;
  gap: 12px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  align-items: center;
}

.toolbar-left .el-dropdown {
  margin-right: 8px;
}

/* Token控制样式 */
.token-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.token-presets {
  margin-top: 8px;
}

.token-presets .el-button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.token-presets .el-button {
  min-width: 50px;
  font-size: 12px;
}
</style>
