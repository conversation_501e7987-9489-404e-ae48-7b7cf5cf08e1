{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-deprecated/index.ts"], "sourcesContent": ["import { unref, watch } from 'vue'\nimport { debugWarn } from '@element-plus/utils'\n\nimport type { MaybeRef } from '@vueuse/core'\n\ntype DeprecationParam = {\n  from: string\n  replacement: string\n  scope: string\n  version: string\n  ref: string\n  type?: 'API' | 'Attribute' | 'Event' | 'Slot'\n}\n\nexport const useDeprecated = (\n  { from, replacement, scope, version, ref, type = 'API' }: DeprecationParam,\n  condition: MaybeRef<boolean>\n) => {\n  watch(\n    () => unref(condition),\n    (val) => {\n      if (val) {\n        debugWarn(\n          scope,\n          `[${type}] ${from} is about to be deprecated in version ${version}, please use ${replacement} instead.\nFor more detail, please visit: ${ref}\n`\n        )\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n}\n"], "names": ["watch", "unref"], "mappings": ";;;;;;AAEY,MAAC,aAAa,GAAG,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,GAAG,KAAK,EAAE,EAAE,SAAS,KAAK;AACtG,EAAEA,SAAK,CAAC,MAAMC,SAAK,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,KAAK;AAMzC,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL;;;;"}