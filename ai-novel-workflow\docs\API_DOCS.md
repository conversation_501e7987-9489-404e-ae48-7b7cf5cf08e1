# API接口文档

本文档详细描述了AI小说生成工作流系统的REST API接口。

## 基本信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功" // 可选
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE" // 可选
}
```

## 项目管理 API

### 获取项目列表
```http
GET /projects
```

**查询参数**:
- `page` (number): 页码，默认1
- `limit` (number): 每页数量，默认10
- `genre` (string): 小说类型筛选
- `status` (string): 状态筛选

**响应示例**:
```json
{
  "success": true,
  "data": {
    "projects": [
      {
        "id": "uuid",
        "name": "我的小说",
        "description": "项目描述",
        "genre": "玄幻",
        "status": "in_progress",
        "word_count": 50000,
        "chapter_count": 10,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-02T00:00:00Z"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  }
}
```

### 获取项目详情
```http
GET /projects/{id}
```

**路径参数**:
- `id` (string): 项目ID

### 创建项目
```http
POST /projects
```

**请求体**:
```json
{
  "name": "项目名称",
  "description": "项目描述",
  "genre": "玄幻",
  "settings": {
    "theme": "dark",
    "style": "formal"
  },
  "tags": ["标签1", "标签2"]
}
```

### 更新项目
```http
PUT /projects/{id}
```

### 删除项目
```http
DELETE /projects/{id}
```

### 获取项目统计
```http
GET /projects/{id}/stats
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "workflows": 5,
    "characters": 12,
    "chapters": 20,
    "totalWords": 80000,
    "averageWordsPerChapter": 4000
  }
}
```

### 导出项目
```http
GET /projects/{id}/export
```

### 导入项目
```http
POST /projects/import
```

## 工作流管理 API

### 获取工作流列表
```http
GET /workflows
```

**查询参数**:
- `project_id` (string): 项目ID筛选
- `type` (string): 工作流类型筛选
- `is_template` (boolean): 是否为模板

### 获取工作流详情
```http
GET /workflows/{id}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "角色创建工作流",
    "description": "自动创建角色的工作流",
    "type": "character_creation",
    "status": "active",
    "nodes": [
      {
        "id": "node1",
        "type": "start",
        "name": "开始",
        "position": { "x": 100, "y": 100 },
        "config": {}
      }
    ],
    "edges": [
      {
        "id": "edge1",
        "source": "node1",
        "target": "node2",
        "type": "default"
      }
    ]
  }
}
```

### 创建工作流
```http
POST /workflows
```

**请求体**:
```json
{
  "project_id": "uuid",
  "name": "工作流名称",
  "description": "工作流描述",
  "type": "custom",
  "nodes": [
    {
      "id": "node1",
      "type": "start",
      "name": "开始",
      "position": { "x": 100, "y": 100 },
      "config": {},
      "promptTemplate": "提示词模板"
    }
  ],
  "edges": [
    {
      "id": "edge1",
      "source": "node1",
      "target": "node2",
      "dataMapping": {
        "output": "input"
      }
    }
  ]
}
```

### 执行工作流
```http
POST /workflows/{id}/execute
```

**请求体**:
```json
{
  "inputData": {
    "characterName": "张三",
    "characterRole": "主角",
    "description": "一个普通的年轻人"
  },
  "options": {
    "stopOnError": true,
    "timeout": 300000
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "executionId": "uuid",
    "result": {
      "node1": {
        "character": {
          "name": "张三",
          "appearance": "...",
          "personality": "..."
        }
      }
    }
  }
}
```

### 停止工作流执行
```http
POST /workflows/{id}/stop/{executionId}
```

### 获取执行日志
```http
GET /workflows/{id}/logs
```

**查询参数**:
- `execution_id` (string): 执行ID筛选
- `limit` (number): 数量限制
- `offset` (number): 偏移量

## 角色管理 API

### 获取角色列表
```http
GET /characters
```

**查询参数**:
- `project_id` (string): 项目ID
- `role` (string): 角色类型
- `is_active` (boolean): 是否激活

### 创建角色
```http
POST /characters
```

**请求体**:
```json
{
  "project_id": "uuid",
  "name": "角色名称",
  "role": "protagonist",
  "gender": "male",
  "age": 25,
  "appearance": "外貌描述",
  "personality": "性格特征",
  "background": "背景故事",
  "skills": ["技能1", "技能2"],
  "relationships": {
    "character_id": "关系描述"
  },
  "goals": "目标动机",
  "conflicts": "内在冲突"
}
```

### 批量创建角色
```http
POST /characters/batch
```

### 获取角色关系图
```http
GET /characters/project/{projectId}/relationships
```

## 章节管理 API

### 获取章节列表
```http
GET /chapters
```

### 创建章节
```http
POST /chapters
```

**请求体**:
```json
{
  "project_id": "uuid",
  "title": "章节标题",
  "content": "章节内容",
  "summary": "章节摘要",
  "outline": "章节大纲",
  "characters_involved": ["character_id1", "character_id2"],
  "plot_points": ["情节点1", "情节点2"],
  "setting": {
    "location": "地点",
    "time": "时间"
  }
}
```

### 保存章节版本
```http
POST /chapters/{id}/save-version
```

### 恢复章节版本
```http
POST /chapters/{id}/restore-version/{version}
```

### 获取章节版本历史
```http
GET /chapters/{id}/versions
```

## AI服务 API

### 生成文本
```http
POST /ai/generate
```

**请求体**:
```json
{
  "prompt": "请写一个角色描述",
  "options": {
    "temperature": 0.7,
    "max_tokens": 2000,
    "top_p": 0.9
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "text": "生成的文本内容",
    "prompt": "原始提示词",
    "options": {
      "temperature": 0.7,
      "max_tokens": 2000
    }
  }
}
```

### 流式生成文本
```http
POST /ai/generate-stream
```

**响应格式**: Server-Sent Events (SSE)
```
data: {"chunk": "生成的文本片段"}
data: {"chunk": "更多文本"}
data: {"done": true}
```

### 检查AI状态
```http
GET /ai/status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "status": "online",
    "models": 3,
    "currentModel": "llama2"
  }
}
```

### 获取可用模型
```http
GET /ai/models
```

### 切换AI模型
```http
POST /ai/switch-model
```

**请求体**:
```json
{
  "modelType": "ollama"
}
```

### 测试AI连接
```http
POST /ai/test
```

## 设置管理 API

### 获取所有设置
```http
GET /settings
```

**查询参数**:
- `category` (string): 设置分类
- `is_public` (boolean): 是否公开

### 更新设置
```http
PUT /settings/{key}
```

**请求体**:
```json
{
  "value": "设置值"
}
```

### 批量更新设置
```http
PATCH /settings/batch
```

**请求体**:
```json
{
  "settings": {
    "theme": "dark",
    "language": "zh-CN",
    "ai_model_type": "ollama"
  }
}
```

### 重置设置
```http
POST /settings/{key}/reset
```

## WebSocket API

### 连接地址
```
ws://localhost:8080/socket.io/
```

### 事件类型

#### 工作流执行事件
- `workflow:started` - 工作流开始执行
- `workflow:completed` - 工作流执行完成
- `workflow:failed` - 工作流执行失败
- `workflow:stopped` - 工作流执行停止

#### 节点执行事件
- `node:started` - 节点开始执行
- `node:completed` - 节点执行完成
- `node:failed` - 节点执行失败

### 事件数据格式
```json
{
  "executionId": "uuid",
  "workflowId": "uuid",
  "nodeId": "node1",
  "nodeName": "角色设定",
  "status": "completed",
  "result": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 错误代码

| 代码 | 说明 |
|------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 限流规则

- 每个IP每15分钟最多100个请求
- AI生成接口每分钟最多10个请求
- 文件上传大小限制100MB

## 认证授权

当前版本暂不支持用户认证，后续版本将添加JWT认证机制。

## SDK示例

### JavaScript
```javascript
import axios from 'axios'

const api = axios.create({
  baseURL: 'http://localhost:8080/api'
})

// 创建项目
const project = await api.post('/projects', {
  name: '我的小说',
  genre: '玄幻'
})

// 生成文本
const result = await api.post('/ai/generate', {
  prompt: '请描述一个角色'
})
```

### Python
```python
import requests

base_url = 'http://localhost:8080/api'

# 创建项目
response = requests.post(f'{base_url}/projects', json={
    'name': '我的小说',
    'genre': '玄幻'
})

# 生成文本
response = requests.post(f'{base_url}/ai/generate', json={
    'prompt': '请描述一个角色'
})
```
