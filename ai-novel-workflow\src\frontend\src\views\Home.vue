<template>
  <div class="home">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>🤖 AI小说生成工作流系统</h1>
          <p>智能化小说创作平台</p>

          <!-- 导航菜单 -->
          <div class="nav-menu">
            <el-button @click="router.push('/')" :type="$route.path === '/' ? 'primary' : 'text'">
              首页
            </el-button>
            <el-button @click="router.push('/projects')" :type="$route.path === '/projects' ? 'primary' : 'text'">
              项目管理
            </el-button>
            <el-button @click="router.push('/settings')" :type="$route.path === '/settings' ? 'primary' : 'text'">
              系统设置
            </el-button>
          </div>
        </div>
      </el-header>
      
      <el-main>
        <div class="welcome-section">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-card class="feature-card">
                <template #header>
                  <div class="card-header">
                    <el-icon><Document /></el-icon>
                    <span>项目管理</span>
                  </div>
                </template>
                <p>创建和管理您的小说项目，支持多项目并行开发</p>
                <el-button type="primary" @click="goToProjects">
                  开始创作
                </el-button>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card">
                <template #header>
                  <div class="card-header">
                    <el-icon><Connection /></el-icon>
                    <span>工作流设计</span>
                  </div>
                </template>
                <p>可视化设计创作流程，自动化内容生成</p>
                <el-button type="primary" @click="goToWorkflows">
                  设计工作流
                </el-button>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <el-card class="feature-card">
                <template #header>
                  <div class="card-header">
                    <el-icon><Setting /></el-icon>
                    <span>AI配置</span>
                  </div>
                </template>
                <p>配置AI模型，支持Ollama、LM Studio等</p>
                <el-button type="primary" @click="goToSettings">
                  配置AI
                </el-button>
              </el-card>
            </el-col>
          </el-row>
        </div>
        
        <div class="status-section">
          <el-card>
            <template #header>
              <div class="card-header">
                <el-icon><Monitor /></el-icon>
                <span>系统状态</span>
              </div>
            </template>
            
            <el-row :gutter="20">
              <el-col :span="6">
                <div class="status-item">
                  <div class="status-label">前端服务</div>
                  <el-tag type="success">运行中</el-tag>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="status-item">
                  <div class="status-label">后端服务</div>
                  <el-tag type="success">运行中</el-tag>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="status-item">
                  <div class="status-label">数据库</div>
                  <el-tag type="success">已连接</el-tag>
                </div>
              </el-col>
              
              <el-col :span="6">
                <div class="status-item">
                  <div class="status-label">AI服务</div>
                  <el-tag type="warning">待配置</el-tag>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Document, Connection, Setting, Monitor } from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()

const goToProjects = () => {
  router.push('/projects')
}

const goToWorkflows = () => {
  ElMessage.info('工作流设计功能开发中...')
}

const goToSettings = () => {
  router.push('/settings')
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.el-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  text-align: center;
  color: white;
  padding: 20px 0;
}

.header-content h1 {
  margin: 0 0 10px 0;
  font-size: 2.5em;
  font-weight: bold;
}

.header-content p {
  margin: 0 0 20px 0;
  font-size: 1.2em;
  opacity: 0.9;
}

.nav-menu {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.nav-menu .el-button {
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.nav-menu .el-button--text {
  color: rgba(255, 255, 255, 0.8);
}

.nav-menu .el-button--primary {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.el-main {
  padding: 40px;
}

.welcome-section {
  margin-bottom: 40px;
}

.feature-card {
  height: 200px;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  font-size: 1.1em;
}

.status-section .el-card {
  background: rgba(255, 255, 255, 0.95);
}

.status-item {
  text-align: center;
  padding: 20px;
}

.status-label {
  margin-bottom: 10px;
  font-weight: bold;
  color: #666;
}
</style>
