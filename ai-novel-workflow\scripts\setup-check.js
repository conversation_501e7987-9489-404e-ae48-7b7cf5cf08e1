#!/usr/bin/env node

/**
 * 系统环境检查脚本
 * 检查系统环境是否满足运行要求
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SetupChecker {
  constructor() {
    this.checks = [];
    this.errors = [];
    this.warnings = [];
  }

  // 记录检查结果
  logCheck(name, status, message, level = 'info') {
    const result = { name, status, message, level };
    this.checks.push(result);

    const icon = status ? '✅' : (level === 'warning' ? '⚠️' : '❌');
    console.log(`${icon} ${name}: ${message}`);

    if (!status) {
      if (level === 'warning') {
        this.warnings.push(result);
      } else {
        this.errors.push(result);
      }
    }
  }

  // 检查Node.js版本
  checkNodeVersion() {
    try {
      const version = process.version;
      const majorVersion = parseInt(version.slice(1).split('.')[0]);
      
      if (majorVersion >= 16) {
        this.logCheck('Node.js版本', true, `${version} (满足要求)`);
        return true;
      } else {
        this.logCheck('Node.js版本', false, `${version} (需要16+)`);
        return false;
      }
    } catch (error) {
      this.logCheck('Node.js版本', false, '无法检测Node.js版本');
      return false;
    }
  }

  // 检查npm版本
  checkNpmVersion() {
    try {
      const version = execSync('npm --version', { encoding: 'utf8' }).trim();
      const majorVersion = parseInt(version.split('.')[0]);
      
      if (majorVersion >= 8) {
        this.logCheck('npm版本', true, `${version} (满足要求)`);
        return true;
      } else {
        this.logCheck('npm版本', false, `${version} (需要8+)`);
        return false;
      }
    } catch (error) {
      this.logCheck('npm版本', false, '无法检测npm版本');
      return false;
    }
  }

  // 检查项目文件结构
  checkProjectStructure() {
    const requiredFiles = [
      'package.json',
      'src/frontend/package.json',
      'src/backend/package.json',
      'src/backend/app.js',
      '.env.example'
    ];

    const requiredDirs = [
      'src',
      'src/frontend',
      'src/backend',
      'docs'
    ];

    let allExists = true;

    // 检查文件
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.logCheck(`文件 ${file}`, true, '存在');
      } else {
        this.logCheck(`文件 ${file}`, false, '缺失');
        allExists = false;
      }
    }

    // 检查目录
    for (const dir of requiredDirs) {
      if (fs.existsSync(dir) && fs.statSync(dir).isDirectory()) {
        this.logCheck(`目录 ${dir}`, true, '存在');
      } else {
        this.logCheck(`目录 ${dir}`, false, '缺失');
        allExists = false;
      }
    }

    return allExists;
  }

  // 检查依赖安装状态
  checkDependencies() {
    const nodeModulesPaths = [
      'node_modules',
      'src/frontend/node_modules',
      'src/backend/node_modules'
    ];

    let allInstalled = true;

    for (const path of nodeModulesPaths) {
      if (fs.existsSync(path)) {
        this.logCheck(`依赖 ${path}`, true, '已安装');
      } else {
        this.logCheck(`依赖 ${path}`, false, '未安装', 'warning');
        allInstalled = false;
      }
    }

    return allInstalled;
  }

  // 检查环境配置
  checkEnvironmentConfig() {
    if (fs.existsSync('.env')) {
      this.logCheck('环境配置', true, '.env文件存在');
      return true;
    } else {
      this.logCheck('环境配置', false, '.env文件不存在', 'warning');
      return false;
    }
  }

  // 检查端口占用
  checkPortAvailability() {
    const ports = [3000, 8080];
    let allAvailable = true;

    for (const port of ports) {
      try {
        // 简单的端口检查（在实际环境中可能需要更复杂的检查）
        const net = require('net');
        const server = net.createServer();
        
        server.listen(port, () => {
          server.close();
          this.logCheck(`端口 ${port}`, true, '可用');
        });

        server.on('error', () => {
          this.logCheck(`端口 ${port}`, false, '被占用', 'warning');
          allAvailable = false;
        });
      } catch (error) {
        this.logCheck(`端口 ${port}`, false, '检查失败', 'warning');
        allAvailable = false;
      }
    }

    return allAvailable;
  }

  // 检查系统资源
  checkSystemResources() {
    const os = require('os');
    
    // 检查内存
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    const totalMemGB = (totalMem / 1024 / 1024 / 1024).toFixed(2);
    const freeMemGB = (freeMem / 1024 / 1024 / 1024).toFixed(2);

    if (totalMem >= 4 * 1024 * 1024 * 1024) { // 4GB
      this.logCheck('系统内存', true, `总内存: ${totalMemGB}GB, 可用: ${freeMemGB}GB`);
    } else {
      this.logCheck('系统内存', false, `总内存: ${totalMemGB}GB (建议4GB+)`, 'warning');
    }

    // 检查CPU
    const cpus = os.cpus();
    this.logCheck('CPU核心数', true, `${cpus.length}核心`);

    // 检查磁盘空间
    try {
      const stats = fs.statSync('.');
      this.logCheck('磁盘访问', true, '正常');
    } catch (error) {
      this.logCheck('磁盘访问', false, '异常');
    }

    return true;
  }

  // 检查可选依赖
  checkOptionalDependencies() {
    const optionalTools = [
      { name: 'git', command: 'git --version' },
      { name: 'docker', command: 'docker --version' },
      { name: 'docker-compose', command: 'docker-compose --version' }
    ];

    for (const tool of optionalTools) {
      try {
        const version = execSync(tool.command, { encoding: 'utf8' }).trim();
        this.logCheck(`可选工具 ${tool.name}`, true, `已安装: ${version.split('\n')[0]}`);
      } catch (error) {
        this.logCheck(`可选工具 ${tool.name}`, false, '未安装', 'warning');
      }
    }

    return true;
  }

  // 生成修复建议
  generateFixSuggestions() {
    const suggestions = [];

    if (this.errors.length > 0) {
      suggestions.push('\n🔧 错误修复建议:');
      
      this.errors.forEach(error => {
        switch (error.name) {
          case 'Node.js版本':
            suggestions.push('- 请安装Node.js 16+: https://nodejs.org/');
            break;
          case 'npm版本':
            suggestions.push('- 请更新npm: npm install -g npm@latest');
            break;
          default:
            if (error.name.startsWith('文件') || error.name.startsWith('目录')) {
              suggestions.push(`- 请确保项目文件完整，重新下载项目`);
            }
        }
      });
    }

    if (this.warnings.length > 0) {
      suggestions.push('\n⚠️ 警告处理建议:');
      
      this.warnings.forEach(warning => {
        switch (warning.name) {
          case '环境配置':
            suggestions.push('- 复制.env.example为.env: cp .env.example .env');
            break;
          default:
            if (warning.name.startsWith('依赖')) {
              suggestions.push('- 安装依赖: npm run install-all');
            } else if (warning.name.startsWith('端口')) {
              suggestions.push('- 检查端口占用，关闭占用进程或修改配置');
            }
        }
      });
    }

    return suggestions;
  }

  // 运行所有检查
  async runAllChecks() {
    console.log('🔍 开始环境检查...\n');

    // 基础环境检查
    console.log('📋 基础环境:');
    this.checkNodeVersion();
    this.checkNpmVersion();
    console.log();

    // 项目结构检查
    console.log('📁 项目结构:');
    this.checkProjectStructure();
    console.log();

    // 依赖检查
    console.log('📦 依赖状态:');
    this.checkDependencies();
    console.log();

    // 配置检查
    console.log('⚙️ 配置文件:');
    this.checkEnvironmentConfig();
    console.log();

    // 系统资源检查
    console.log('💻 系统资源:');
    this.checkSystemResources();
    console.log();

    // 可选工具检查
    console.log('🛠️ 可选工具:');
    this.checkOptionalDependencies();
    console.log();

    // 生成报告
    this.generateReport();
  }

  // 生成检查报告
  generateReport() {
    const totalChecks = this.checks.length;
    const passedChecks = this.checks.filter(c => c.status).length;
    const errorCount = this.errors.length;
    const warningCount = this.warnings.length;

    console.log('📊 检查报告:');
    console.log(`总检查项: ${totalChecks}`);
    console.log(`通过: ${passedChecks}`);
    console.log(`错误: ${errorCount}`);
    console.log(`警告: ${warningCount}`);

    if (errorCount === 0) {
      console.log('\n✅ 环境检查通过！系统可以正常启动。');
    } else {
      console.log('\n❌ 发现严重问题，请先解决错误后再启动系统。');
    }

    if (warningCount > 0) {
      console.log('⚠️ 发现一些警告，建议处理后获得更好体验。');
    }

    // 显示修复建议
    const suggestions = this.generateFixSuggestions();
    if (suggestions.length > 0) {
      console.log(suggestions.join('\n'));
    }

    console.log('\n📚 更多帮助:');
    console.log('- 安装指南: docs/INSTALL.md');
    console.log('- 用户手册: docs/USER_GUIDE.md');
    console.log('- 启动系统: npm run dev 或 ./start.sh');

    return errorCount === 0;
  }
}

// 运行检查
if (require.main === module) {
  const checker = new SetupChecker();
  checker.runAllChecks()
    .then(() => {
      process.exit(checker.errors.length === 0 ? 0 : 1);
    })
    .catch(error => {
      console.error('检查过程出错:', error);
      process.exit(1);
    });
}

module.exports = SetupChecker;
