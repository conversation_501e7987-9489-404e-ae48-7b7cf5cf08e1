# 用户使用手册

欢迎使用AI小说生成工作流系统！本手册将帮助您快速上手并充分利用系统的各项功能。

## 快速开始

### 第一次使用

1. **启动系统**
   - Windows: 双击 `start.bat`
   - Linux/Mac: 运行 `./start.sh`
   - 等待服务启动完成

2. **访问系统**
   - 打开浏览器访问：http://localhost:3000
   - 首次访问会自动进入设置向导

3. **配置AI模型**
   - 进入"系统设置"页面
   - 选择AI模型类型（Ollama/LM Studio/LocalAI）
   - 配置连接参数并测试连接

## 核心概念

### 项目（Project）
项目是小说创作的顶层容器，包含：
- 基本信息（名称、描述、类型）
- 角色管理
- 章节内容
- 工作流定义

### 工作流（Workflow）
工作流是自动化创作流程的核心，通过连接不同的节点来实现：
- 角色设定
- 世界观构建
- 情节生成
- 章节写作
- 内容润色

### 节点类型
- **开始节点**: 工作流入口
- **结束节点**: 工作流出口
- **角色设定节点**: 创建和完善角色信息
- **世界观设定节点**: 构建故事背景和设定
- **情节大纲节点**: 生成故事结构和情节
- **章节生成节点**: 根据大纲生成具体章节
- **内容润色节点**: 优化和改进文本内容
- **条件判断节点**: 根据条件进行流程分支

## 功能详解

### 项目管理

#### 创建项目
1. 点击"项目管理"菜单
2. 点击"新建项目"按钮
3. 填写项目信息：
   - **项目名称**: 必填，项目的标识名称
   - **项目描述**: 简要描述项目内容
   - **小说类型**: 选择类型（玄幻、都市、科幻等）
   - **标签**: 添加相关标签便于分类

#### 项目设置
- **基本信息**: 修改项目名称、描述等
- **封面设置**: 上传项目封面图片
- **导出设置**: 配置导出格式和选项

#### 项目导入导出
- **导出**: 将项目数据导出为JSON文件
- **导入**: 从JSON文件导入项目数据

### 角色管理

#### 创建角色
1. 进入项目详情页面
2. 点击"角色管理"标签
3. 点击"添加角色"按钮
4. 填写角色信息：
   - **基本信息**: 姓名、性别、年龄
   - **外貌描述**: 详细的外貌特征
   - **性格特征**: 性格、习惯、特点
   - **背景故事**: 成长经历、重要事件
   - **技能特长**: 擅长的技能和能力
   - **目标动机**: 角色的目标和动机
   - **人际关系**: 与其他角色的关系

#### 角色关系图
- 可视化显示角色之间的关系
- 支持拖拽调整布局
- 点击连线可编辑关系详情

### 工作流编辑器

#### 创建工作流
1. 进入"工作流管理"页面
2. 点击"新建工作流"
3. 选择工作流类型或从模板创建
4. 进入可视化编辑器

#### 节点操作
- **添加节点**: 从左侧节点面板拖拽到画布
- **连接节点**: 拖拽节点的连接点创建连线
- **配置节点**: 双击节点打开配置面板
- **删除节点**: 选中节点按Delete键

#### 节点配置

**角色设定节点配置**:
```
输入参数:
- 角色名称
- 角色类型 (主角/配角/反派)
- 基本描述

提示词模板:
请根据以下信息创建详细的角色设定：
角色名称：{{name}}
角色类型：{{role}}
基本信息：{{description}}
...
```

**章节生成节点配置**:
```
输入参数:
- 章节标题
- 章节大纲
- 涉及角色
- 字数要求

输出格式:
- 章节内容
- 字数统计
- 涉及角色列表
```

#### 执行工作流
1. 配置完成后点击"保存"
2. 点击"执行"按钮
3. 填写初始输入数据
4. 监控执行进度
5. 查看执行结果

### 章节管理

#### 章节列表
- 显示所有章节的基本信息
- 支持按状态筛选
- 支持批量操作

#### 章节编辑器
- **富文本编辑**: 支持格式化文本编辑
- **实时字数统计**: 显示当前字数
- **自动保存**: 定期自动保存内容
- **版本管理**: 支持保存和恢复历史版本

#### 版本控制
- **保存版本**: 手动保存当前版本
- **版本历史**: 查看所有历史版本
- **版本对比**: 对比不同版本的差异
- **版本恢复**: 恢复到指定版本

### 模板库

#### 预设模板
系统提供多种预设工作流模板：
- **玄幻小说创作流程**
- **都市言情创作流程**
- **科幻小说创作流程**
- **悬疑推理创作流程**

#### 自定义模板
- 将工作流保存为模板
- 设置模板分类和描述
- 分享模板给其他用户

### 系统设置

#### AI模型配置
- **模型类型**: 选择使用的AI模型
- **连接参数**: 配置API地址和模型名称
- **生成参数**: 调整温度、最大令牌数等
- **连接测试**: 测试AI模型连接状态

#### 界面设置
- **主题切换**: 亮色/暗色主题
- **语言设置**: 界面语言选择
- **布局设置**: 自定义界面布局

#### 导出设置
- **格式选择**: 支持多种导出格式
- **内容选项**: 选择导出的内容范围
- **样式设置**: 自定义导出样式

## 最佳实践

### 工作流设计原则

1. **模块化设计**
   - 将复杂流程拆分为简单节点
   - 每个节点职责单一明确
   - 便于调试和维护

2. **数据流设计**
   - 明确节点间的数据传递
   - 使用数据映射配置
   - 避免数据冗余

3. **错误处理**
   - 添加条件判断节点
   - 设置错误处理分支
   - 记录执行日志

### 提示词优化

1. **明确指令**
   - 使用清晰具体的指令
   - 避免模糊的描述
   - 提供充足的上下文

2. **结构化输出**
   - 要求AI按特定格式输出
   - 使用JSON格式便于解析
   - 设置输出示例

3. **迭代优化**
   - 根据输出结果调整提示词
   - 测试不同的表达方式
   - 记录有效的提示词模板

### 项目组织

1. **命名规范**
   - 使用有意义的名称
   - 保持命名一致性
   - 添加必要的描述

2. **分类管理**
   - 使用标签分类项目
   - 按类型组织工作流
   - 定期整理和归档

3. **备份策略**
   - 定期导出项目数据
   - 保存重要版本
   - 使用版本控制

## 故障排除

### 常见问题

**Q: AI生成内容质量不佳**
A: 优化建议：
- 改进提示词描述
- 调整生成参数
- 增加上下文信息
- 使用更好的AI模型

**Q: 工作流执行失败**
A: 检查项目：
- 查看执行日志
- 检查节点配置
- 验证数据格式
- 测试AI连接

**Q: 系统响应缓慢**
A: 优化方案：
- 减少并发执行
- 调整生成参数
- 清理历史数据
- 升级硬件配置

### 日志查看
- 系统日志：`logs/app.log`
- 执行日志：工作流管理页面
- 错误信息：浏览器控制台

### 数据恢复
如果数据丢失，可以：
1. 从备份文件恢复
2. 检查数据库文件
3. 查看历史版本
4. 联系技术支持

## 高级功能

### API集成
系统提供完整的REST API，支持：
- 项目管理
- 工作流执行
- 内容生成
- 数据导入导出

### 插件扩展
支持自定义节点类型：
1. 实现节点处理器
2. 注册节点类型
3. 配置节点界面
4. 测试节点功能

### 批量处理
支持批量操作：
- 批量创建角色
- 批量生成章节
- 批量执行工作流
- 批量导出内容

## 技术支持

如需帮助，请：
1. 查看本用户手册
2. 检查常见问题解答
3. 查看系统日志
4. 提交Issue到项目仓库
5. 联系技术支持团队
