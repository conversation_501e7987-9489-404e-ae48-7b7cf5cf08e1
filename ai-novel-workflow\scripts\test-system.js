#!/usr/bin/env node

/**
 * 系统集成测试脚本
 * 测试整个系统的功能完整性
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:8080';
const API_URL = `${BASE_URL}/api`;

class SystemTester {
  constructor() {
    this.testResults = [];
    this.projectId = null;
    this.workflowId = null;
    this.characterId = null;
    this.chapterId = null;
  }

  // 记录测试结果
  logResult(testName, success, message = '', data = null) {
    const result = {
      test: testName,
      success,
      message,
      data,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    if (!success) {
      console.error('  错误详情:', data);
    }
  }

  // 等待服务启动
  async waitForServer(maxAttempts = 30) {
    console.log('🔍 等待服务器启动...');
    
    for (let i = 0; i < maxAttempts; i++) {
      try {
        await axios.get(`${BASE_URL}/health`);
        this.logResult('服务器连接', true, '服务器已启动');
        return true;
      } catch (error) {
        if (i === maxAttempts - 1) {
          this.logResult('服务器连接', false, '服务器启动超时', error.message);
          return false;
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  // 测试健康检查
  async testHealthCheck() {
    try {
      const response = await axios.get(`${BASE_URL}/health`);
      
      if (response.status === 200 && response.data.status === 'ok') {
        this.logResult('健康检查', true, '系统状态正常');
        return true;
      } else {
        this.logResult('健康检查', false, '系统状态异常', response.data);
        return false;
      }
    } catch (error) {
      this.logResult('健康检查', false, '健康检查失败', error.message);
      return false;
    }
  }

  // 测试AI服务
  async testAIService() {
    try {
      // 检查AI状态
      const statusResponse = await axios.get(`${API_URL}/ai/status`);
      
      if (statusResponse.data.success) {
        this.logResult('AI状态检查', true, `AI状态: ${statusResponse.data.data.status}`);
      } else {
        this.logResult('AI状态检查', false, 'AI状态检查失败', statusResponse.data);
      }

      // 测试AI连接
      const testResponse = await axios.post(`${API_URL}/ai/test`);
      
      if (testResponse.data.success) {
        this.logResult('AI连接测试', true, 'AI连接正常');
        return true;
      } else {
        this.logResult('AI连接测试', false, 'AI连接失败', testResponse.data);
        return false;
      }
    } catch (error) {
      this.logResult('AI服务测试', false, 'AI服务测试失败', error.message);
      return false;
    }
  }

  // 测试项目管理
  async testProjectManagement() {
    try {
      // 创建项目
      const createData = {
        name: '系统测试项目',
        description: '用于系统集成测试的项目',
        genre: '玄幻',
        tags: ['测试', '自动化']
      };

      const createResponse = await axios.post(`${API_URL}/projects`, createData);
      
      if (createResponse.data.success) {
        this.projectId = createResponse.data.data.id;
        this.logResult('项目创建', true, `项目ID: ${this.projectId}`);
      } else {
        this.logResult('项目创建', false, '项目创建失败', createResponse.data);
        return false;
      }

      // 获取项目详情
      const getResponse = await axios.get(`${API_URL}/projects/${this.projectId}`);
      
      if (getResponse.data.success && getResponse.data.data.name === createData.name) {
        this.logResult('项目查询', true, '项目信息正确');
      } else {
        this.logResult('项目查询', false, '项目信息不匹配', getResponse.data);
        return false;
      }

      // 更新项目
      const updateData = { description: '更新后的描述' };
      const updateResponse = await axios.put(`${API_URL}/projects/${this.projectId}`, updateData);
      
      if (updateResponse.data.success) {
        this.logResult('项目更新', true, '项目更新成功');
      } else {
        this.logResult('项目更新', false, '项目更新失败', updateResponse.data);
        return false;
      }

      return true;
    } catch (error) {
      this.logResult('项目管理测试', false, '项目管理测试失败', error.message);
      return false;
    }
  }

  // 测试角色管理
  async testCharacterManagement() {
    if (!this.projectId) {
      this.logResult('角色管理测试', false, '缺少项目ID');
      return false;
    }

    try {
      // 创建角色
      const characterData = {
        project_id: this.projectId,
        name: '测试角色',
        role: 'protagonist',
        gender: 'male',
        age: 20,
        appearance: '英俊潇洒的年轻人',
        personality: '勇敢、正义、有责任感',
        background: '出身普通但有着不凡的经历',
        skills: ['剑法', '内功'],
        goals: '成为最强的武者'
      };

      const createResponse = await axios.post(`${API_URL}/characters`, characterData);
      
      if (createResponse.data.success) {
        this.characterId = createResponse.data.data.id;
        this.logResult('角色创建', true, `角色ID: ${this.characterId}`);
      } else {
        this.logResult('角色创建', false, '角色创建失败', createResponse.data);
        return false;
      }

      // 获取角色列表
      const listResponse = await axios.get(`${API_URL}/characters?project_id=${this.projectId}`);
      
      if (listResponse.data.success && listResponse.data.data.length > 0) {
        this.logResult('角色列表', true, `找到${listResponse.data.data.length}个角色`);
      } else {
        this.logResult('角色列表', false, '角色列表为空', listResponse.data);
        return false;
      }

      return true;
    } catch (error) {
      this.logResult('角色管理测试', false, '角色管理测试失败', error.message);
      return false;
    }
  }

  // 测试工作流管理
  async testWorkflowManagement() {
    if (!this.projectId) {
      this.logResult('工作流管理测试', false, '缺少项目ID');
      return false;
    }

    try {
      // 创建简单工作流
      const workflowData = {
        project_id: this.projectId,
        name: '测试工作流',
        description: '用于测试的简单工作流',
        type: 'custom',
        nodes: [
          {
            id: 'start',
            type: 'start',
            name: '开始',
            position: { x: 100, y: 100 },
            config: {}
          },
          {
            id: 'character-setup',
            type: 'character_setup',
            name: '角色设定',
            position: { x: 300, y: 100 },
            config: { temperature: 0.7, max_tokens: 1000 },
            promptTemplate: '请创建一个角色：姓名{{name}}，类型{{role}}'
          },
          {
            id: 'end',
            type: 'end',
            name: '结束',
            position: { x: 500, y: 100 },
            config: {}
          }
        ],
        edges: [
          {
            id: 'edge1',
            source: 'start',
            target: 'character-setup',
            type: 'default'
          },
          {
            id: 'edge2',
            source: 'character-setup',
            target: 'end',
            type: 'default'
          }
        ]
      };

      const createResponse = await axios.post(`${API_URL}/workflows`, workflowData);
      
      if (createResponse.data.success) {
        this.workflowId = createResponse.data.data.id;
        this.logResult('工作流创建', true, `工作流ID: ${this.workflowId}`);
      } else {
        this.logResult('工作流创建', false, '工作流创建失败', createResponse.data);
        return false;
      }

      // 执行工作流
      const executeData = {
        inputData: {
          name: '自动测试角色',
          role: '主角'
        }
      };

      const executeResponse = await axios.post(`${API_URL}/workflows/${this.workflowId}/execute`, executeData);
      
      if (executeResponse.data.success) {
        this.logResult('工作流执行', true, `执行ID: ${executeResponse.data.data.executionId}`);
      } else {
        this.logResult('工作流执行', false, '工作流执行失败', executeResponse.data);
        return false;
      }

      return true;
    } catch (error) {
      this.logResult('工作流管理测试', false, '工作流管理测试失败', error.message);
      return false;
    }
  }

  // 测试章节管理
  async testChapterManagement() {
    if (!this.projectId) {
      this.logResult('章节管理测试', false, '缺少项目ID');
      return false;
    }

    try {
      // 创建章节
      const chapterData = {
        project_id: this.projectId,
        title: '第一章 测试章节',
        content: '这是一个测试章节的内容。\n\n主角出场，展现其性格特点。\n\n设置悬念，为后续情节做铺垫。',
        summary: '测试章节，介绍主角',
        outline: '1. 主角出场\n2. 性格展现\n3. 设置悬念',
        characters_involved: this.characterId ? [this.characterId] : [],
        plot_points: ['主角出场', '性格展现'],
        setting: {
          location: '测试地点',
          time: '测试时间'
        }
      };

      const createResponse = await axios.post(`${API_URL}/chapters`, chapterData);
      
      if (createResponse.data.success) {
        this.chapterId = createResponse.data.data.id;
        this.logResult('章节创建', true, `章节ID: ${this.chapterId}`);
      } else {
        this.logResult('章节创建', false, '章节创建失败', createResponse.data);
        return false;
      }

      // 保存版本
      const versionResponse = await axios.post(`${API_URL}/chapters/${this.chapterId}/save-version`, {
        versionNote: '测试版本'
      });
      
      if (versionResponse.data.success) {
        this.logResult('章节版本保存', true, '版本保存成功');
      } else {
        this.logResult('章节版本保存', false, '版本保存失败', versionResponse.data);
      }

      return true;
    } catch (error) {
      this.logResult('章节管理测试', false, '章节管理测试失败', error.message);
      return false;
    }
  }

  // 测试设置管理
  async testSettingsManagement() {
    try {
      // 获取设置列表
      const listResponse = await axios.get(`${API_URL}/settings`);
      
      if (listResponse.data.success) {
        this.logResult('设置列表', true, `找到${Object.keys(listResponse.data.data).length}个设置`);
      } else {
        this.logResult('设置列表', false, '获取设置列表失败', listResponse.data);
        return false;
      }

      return true;
    } catch (error) {
      this.logResult('设置管理测试', false, '设置管理测试失败', error.message);
      return false;
    }
  }

  // 清理测试数据
  async cleanup() {
    console.log('\n🧹 清理测试数据...');

    try {
      if (this.projectId) {
        await axios.delete(`${API_URL}/projects/${this.projectId}`);
        this.logResult('清理项目', true, '测试项目已删除');
      }
    } catch (error) {
      this.logResult('清理项目', false, '清理项目失败', error.message);
    }
  }

  // 生成测试报告
  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = ((passedTests / totalTests) * 100).toFixed(2);

    const report = {
      summary: {
        total: totalTests,
        passed: passedTests,
        failed: failedTests,
        successRate: `${successRate}%`
      },
      results: this.testResults,
      timestamp: new Date().toISOString()
    };

    // 保存报告到文件
    const reportPath = path.join(__dirname, '../logs/test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 测试报告:');
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过: ${passedTests}`);
    console.log(`失败: ${failedTests}`);
    console.log(`成功率: ${successRate}%`);
    console.log(`报告已保存到: ${reportPath}`);

    return report;
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始系统集成测试...\n');

    // 等待服务器启动
    if (!(await this.waitForServer())) {
      console.error('❌ 服务器启动失败，测试终止');
      return false;
    }

    // 运行测试套件
    const tests = [
      () => this.testHealthCheck(),
      () => this.testAIService(),
      () => this.testProjectManagement(),
      () => this.testCharacterManagement(),
      () => this.testWorkflowManagement(),
      () => this.testChapterManagement(),
      () => this.testSettingsManagement()
    ];

    let allPassed = true;

    for (const test of tests) {
      try {
        const result = await test();
        if (!result) {
          allPassed = false;
        }
      } catch (error) {
        console.error('测试执行错误:', error);
        allPassed = false;
      }
    }

    // 清理测试数据
    await this.cleanup();

    // 生成报告
    this.generateReport();

    console.log(allPassed ? '\n✅ 所有测试通过！' : '\n❌ 部分测试失败！');
    return allPassed;
  }
}

// 运行测试
if (require.main === module) {
  const tester = new SystemTester();
  tester.runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = SystemTester;
