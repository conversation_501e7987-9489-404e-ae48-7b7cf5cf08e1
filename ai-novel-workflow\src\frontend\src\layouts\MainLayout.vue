<template>
  <div class="layout-container">
    <!-- 顶部导航栏 -->
    <header class="layout-header">
      <div class="header-left">
        <el-button 
          type="text" 
          @click="toggleSidebar"
          class="sidebar-toggle"
          v-if="isMobile"
        >
          <el-icon><Menu /></el-icon>
        </el-button>
        
        <h1 class="app-title">
          <el-icon class="title-icon"><EditPen /></el-icon>
          AI小说工作流
        </h1>
      </div>
      
      <div class="header-center">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item 
            v-for="item in breadcrumbs" 
            :key="item.path"
            :to="item.path"
          >
            {{ item.title }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      
      <div class="header-right">
        <!-- AI状态指示器 -->
        <el-tooltip content="AI模型状态" placement="bottom">
          <el-badge 
            :value="aiStatus.status === 'online' ? '' : '!'" 
            :type="aiStatus.status === 'online' ? 'success' : 'danger'"
            class="ai-status-badge"
          >
            <el-button 
              type="text" 
              @click="checkAIStatus"
              :loading="checkingAI"
            >
              <el-icon :color="aiStatus.status === 'online' ? '#67c23a' : '#f56c6c'">
                <Connection />
              </el-icon>
            </el-button>
          </el-badge>
        </el-tooltip>
        
        <!-- 主题切换 -->
        <el-tooltip :content="isDark ? '切换到亮色主题' : '切换到暗色主题'" placement="bottom">
          <el-button type="text" @click="toggleTheme">
            <el-icon>
              <Sunny v-if="isDark" />
              <Moon v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
        
        <!-- 设置 -->
        <el-tooltip content="系统设置" placement="bottom">
          <el-button type="text" @click="$router.push('/settings')">
            <el-icon><Setting /></el-icon>
          </el-button>
        </el-tooltip>
      </div>
    </header>
    
    <!-- 主体内容 -->
    <main class="layout-main">
      <!-- 侧边栏 -->
      <aside class="layout-sidebar" :class="{ open: sidebarOpen }">
        <nav class="sidebar-nav">
          <el-menu
            :default-active="$route.path"
            router
            :collapse="false"
            class="sidebar-menu"
          >
            <el-menu-item index="/projects">
              <el-icon><Folder /></el-icon>
              <span>项目管理</span>
            </el-menu-item>
            
            <el-menu-item index="/workflow">
              <el-icon><Connection /></el-icon>
              <span>工作流设计器</span>
            </el-menu-item>
            
            <el-menu-item index="/characters">
              <el-icon><User /></el-icon>
              <span>角色管理</span>
            </el-menu-item>
            
            <el-menu-item index="/chapters">
              <el-icon><Document /></el-icon>
              <span>章节管理</span>
            </el-menu-item>
            
            <el-menu-item index="/templates">
              <el-icon><Collection /></el-icon>
              <span>模板库</span>
            </el-menu-item>
          </el-menu>
        </nav>
        
        <!-- 当前项目信息 -->
        <div class="current-project" v-if="currentProject">
          <div class="project-info">
            <div class="project-name">{{ currentProject.name }}</div>
            <div class="project-stats">
              <span>{{ currentProject.chapter_count || 0 }}章</span>
              <span>{{ formatWordCount(currentProject.word_count || 0) }}</span>
            </div>
          </div>
        </div>
      </aside>
      
      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useSettingsStore } from '@/stores/settings'
import { aiAPI } from '@/utils/api'
import { ElMessage } from 'element-plus'

const route = useRoute()
const settingsStore = useSettingsStore()

// 响应式数据
const sidebarOpen = ref(false)
const isMobile = ref(false)
const aiStatus = ref({ status: 'unknown' })
const checkingAI = ref(false)
const currentProject = ref(null)

// 计算属性
const isDark = computed(() => settingsStore.theme === 'dark')

const breadcrumbs = computed(() => {
  const crumbs = []
  const pathSegments = route.path.split('/').filter(Boolean)
  
  let currentPath = ''
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`
    
    // 根据路径生成面包屑
    let title = segment
    if (segment === 'projects') title = '项目'
    else if (segment === 'workflows') title = '工作流'
    else if (segment === 'characters') title = '角色'
    else if (segment === 'chapters') title = '章节'
    else if (segment === 'templates') title = '模板'
    else if (segment === 'settings') title = '设置'
    else if (segment === 'edit') title = '编辑'
    
    crumbs.push({
      path: currentPath,
      title: title
    })
  })
  
  return crumbs
})

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const toggleTheme = () => {
  settingsStore.toggleTheme()
}

const checkAIStatus = async () => {
  checkingAI.value = true
  try {
    const response = await aiAPI.checkStatus()
    aiStatus.value = response.data
    
    if (aiStatus.value.status === 'online') {
      ElMessage.success('AI模型连接正常')
    } else {
      ElMessage.warning('AI模型连接异常')
    }
  } catch (error) {
    aiStatus.value = { status: 'offline' }
    ElMessage.error('检查AI状态失败')
  } finally {
    checkingAI.value = false
  }
}

const formatWordCount = (count) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万字`
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}k字`
  }
  return `${count}字`
}

const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768
  if (!isMobile.value) {
    sidebarOpen.value = false
  }
}

// 生命周期
onMounted(() => {
  checkScreenSize()
  checkAIStatus()
  
  window.addEventListener('resize', checkScreenSize)
})

// 监听路由变化，在移动端自动关闭侧边栏
watch(() => route.path, () => {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
})
</script>

<style scoped>
.layout-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0 20px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin: 0;
}

.title-icon {
  color: var(--el-color-primary);
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-status-badge {
  margin-right: 8px;
}

.sidebar-nav {
  padding: 16px 0;
}

.sidebar-menu {
  border: none;
}

.current-project {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-bg-color);
}

.project-info {
  text-align: center;
}

.project-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
  font-size: 14px;
}

.project-stats {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  display: flex;
  justify-content: center;
  gap: 12px;
}

.sidebar-toggle {
  display: none;
}

@media (max-width: 768px) {
  .sidebar-toggle {
    display: inline-flex;
  }
  
  .layout-sidebar {
    position: fixed;
    top: 60px;
    left: -250px;
    height: calc(100vh - 60px);
    z-index: 1000;
    transition: left 0.3s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  }
  
  .layout-sidebar.open {
    left: 0;
  }
  
  .header-center {
    display: none;
  }
}
</style>
