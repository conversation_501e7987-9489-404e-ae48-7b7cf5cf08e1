{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/watermark/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Watermark from './src/watermark.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElWatermark: SFCWithInstall<typeof Watermark> =\n  withInstall(Watermark)\nexport default ElWatermark\n\nexport * from './src/watermark'\n"], "names": ["withInstall", "Watermark"], "mappings": ";;;;;;;;AAEY,MAAC,WAAW,GAAGA,mBAAW,CAACC,sBAAS;;;;;;"}