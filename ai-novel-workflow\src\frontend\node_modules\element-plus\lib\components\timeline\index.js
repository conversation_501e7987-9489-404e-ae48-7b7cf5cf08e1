'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var timeline = require('./src/timeline.js');
var timelineItem$1 = require('./src/timeline-item2.js');
var timelineItem = require('./src/timeline-item.js');
var tokens = require('./src/tokens.js');
var install = require('../../utils/vue/install.js');

const ElTimeline = install.withInstall(timeline["default"], {
  TimelineItem: timelineItem$1["default"]
});
const ElTimelineItem = install.withNoopInstall(timelineItem$1["default"]);

exports.timelineItemProps = timelineItem.timelineItemProps;
exports.TIMELINE_INJECTION_KEY = tokens.TIMELINE_INJECTION_KEY;
exports.ElTimeline = ElTimeline;
exports.ElTimelineItem = ElTimelineItem;
exports["default"] = ElTimeline;
//# sourceMappingURL=index.js.map
