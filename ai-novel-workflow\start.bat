@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo    AI小说生成工作流系统 - 启动脚本
echo ========================================
echo.

:: 设置当前目录
cd /d "%~dp0"

:: 检查Node.js是否安装
echo [1/7] 检查Node.js环境...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js，请先安装Node.js 16+
    echo 下载地址: https://nodejs.org/
    echo.
    pause
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: Node.js无法正常运行
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version 2^>nul') do set NODE_VERSION=%%i
echo ✅ Node.js版本: !NODE_VERSION!

:: 检查npm是否可用
echo [2/7] 检查npm环境...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到npm
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: npm无法正常运行
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('npm --version 2^>nul') do set NPM_VERSION=%%i
echo ✅ npm版本: !NPM_VERSION!

:: 检查项目文件
echo [3/7] 检查项目文件...
if not exist "package.json" (
    echo ❌ 错误: 找不到package.json文件
    echo 请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

if not exist "src" (
    echo ❌ 错误: 找不到src目录
    echo 请确保项目文件完整
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过

:: 检查是否首次运行
echo [4/7] 检查依赖安装状态...
set NEED_INSTALL=0

if not exist "node_modules" set NEED_INSTALL=1
if not exist "src\frontend\node_modules" set NEED_INSTALL=1
if not exist "src\backend\node_modules" set NEED_INSTALL=1

if !NEED_INSTALL! equ 1 (
    echo 📦 需要安装依赖，正在安装...
    echo.

    :: 安装根目录依赖
    echo 安装根目录依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 根目录依赖安装失败
        pause
        exit /b 1
    )

    :: 安装前端依赖
    echo 安装前端依赖...
    cd src\frontend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..

    :: 安装后端依赖
    echo 安装后端依赖...
    cd src\backend
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..

    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已安装
)

:: 检查环境变量文件
echo [5/7] 检查环境配置...
if not exist ".env" (
    if exist ".env.example" (
        echo 📝 创建环境配置文件...
        copy ".env.example" ".env" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✅ 已创建.env文件
        ) else (
            echo ⚠️ 创建.env文件失败，将使用默认配置
        )
    ) else (
        echo ⚠️ 未找到.env.example文件，将使用默认配置
    )
) else (
    echo ✅ 环境配置文件存在
)

:: 创建必要的目录
echo [6/7] 创建必要目录...
if not exist "database" mkdir database >nul 2>&1
if not exist "uploads" mkdir uploads >nul 2>&1
if not exist "logs" mkdir logs >nul 2>&1
echo ✅ 目录创建完成

:: 启动服务
echo [7/7] 启动服务...
echo.
echo 🚀 正在启动AI小说生成工作流系统...
echo.
echo 前端地址: http://localhost:3000
echo 后端API: http://localhost:8080
echo 健康检查: http://localhost:8080/health
echo.
echo 按 Ctrl+C 停止服务
echo.

:: 启动开发服务器
call npm run dev

if %errorlevel% neq 0 (
    echo.
    echo ❌ 服务启动失败
    echo.
    echo 可能的解决方案:
    echo 1. 检查端口3000和8080是否被占用
    echo 2. 检查防火墙设置
    echo 3. 重新安装依赖
    echo 4. 查看错误日志获取详细信息
    echo.
    pause
    exit /b 1
)

pause
