# 安装配置指南

本文档详细介绍了AI小说生成工作流系统的安装和配置过程。

## 系统要求

### 最低要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 16.0.0 或更高版本
- **内存**: 4GB RAM
- **存储**: 2GB 可用空间
- **网络**: 互联网连接（用于下载依赖）

### 推荐配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Node.js**: 18.0.0 或更高版本
- **内存**: 8GB RAM 或更多
- **存储**: 10GB 可用空间
- **CPU**: 4核心或更多

## 安装方式

### 方式一：一键启动（推荐）

#### Windows
1. 下载项目到本地
2. 双击运行 `start.bat`
3. 等待自动安装依赖和启动服务

#### Linux/macOS
1. 下载项目到本地
2. 在终端中执行：
   ```bash
   chmod +x start.sh
   ./start.sh
   ```

### 方式二：手动安装

#### 1. 安装Node.js

**Windows:**
- 访问 [Node.js官网](https://nodejs.org/)
- 下载LTS版本并安装
- 验证安装：`node --version`

**Ubuntu/Debian:**
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

**CentOS/RHEL:**
```bash
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

**macOS:**
```bash
# 使用Homebrew
brew install node

# 或使用MacPorts
sudo port install nodejs18
```

#### 2. 克隆项目
```bash
git clone <repository-url>
cd ai-novel-workflow
```

#### 3. 安装依赖
```bash
# 安装所有依赖
npm run install-all

# 或分别安装
npm install
cd src/frontend && npm install
cd ../backend && npm install
```

#### 4. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
nano .env  # Linux/macOS
notepad .env  # Windows
```

#### 5. 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

### 方式三：Docker部署

#### 前提条件
- 安装 [Docker](https://www.docker.com/get-started)
- 安装 [Docker Compose](https://docs.docker.com/compose/install/)

#### 部署步骤
```bash
# 克隆项目
git clone <repository-url>
cd ai-novel-workflow

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 环境配置

### 基本配置

编辑 `.env` 文件：

```env
# 服务器配置
PORT=8080
NODE_ENV=development

# 数据库配置
DB_PATH=./database/novel_workflow.db

# AI模型配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# 其他配置...
```

### AI模型配置

系统支持多种本地AI模型，需要单独安装和配置：

#### Ollama
1. 安装Ollama：
   ```bash
   # Linux
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # macOS
   brew install ollama
   
   # Windows
   # 下载安装包：https://ollama.ai/download
   ```

2. 下载模型：
   ```bash
   ollama pull llama2
   ollama pull codellama
   ```

3. 启动服务：
   ```bash
   ollama serve
   ```

#### LM Studio
1. 下载并安装 [LM Studio](https://lmstudio.ai/)
2. 在LM Studio中下载所需模型
3. 启动本地服务器（默认端口1234）

#### LocalAI
1. 使用Docker运行：
   ```bash
   docker run -p 8080:8080 --name local-ai -ti localai/localai:latest
   ```

2. 或从源码编译安装

### 网络配置

#### 防火墙设置
确保以下端口可访问：
- **3000**: 前端服务
- **8080**: 后端API
- **11434**: Ollama服务（如使用）
- **1234**: LM Studio服务（如使用）

#### 代理设置
如果在代理环境下，需要配置npm代理：
```bash
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080
```

## 验证安装

### 1. 检查服务状态
访问以下地址验证服务是否正常：
- 前端界面：http://localhost:3000
- 后端API：http://localhost:8080/health
- API文档：http://localhost:8080/api/docs

### 2. 测试AI连接
在系统设置中测试AI模型连接：
1. 进入"系统设置"页面
2. 配置AI模型参数
3. 点击"测试连接"按钮

### 3. 创建测试项目
1. 创建新项目
2. 添加角色和章节
3. 创建简单工作流
4. 执行工作流测试

## 常见问题

### 安装问题

**Q: npm install失败**
A: 尝试以下解决方案：
```bash
# 清理缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 使用国内镜像
npm config set registry https://registry.npmmirror.com
```

**Q: 端口被占用**
A: 修改端口配置：
```bash
# 查看端口占用
netstat -ano | findstr :3000  # Windows
lsof -i :3000  # Linux/macOS

# 修改.env文件中的端口配置
PORT=8081
```

### 运行问题

**Q: 前端页面无法访问**
A: 检查以下项目：
1. 确认前端服务已启动
2. 检查防火墙设置
3. 确认端口配置正确

**Q: AI模型连接失败**
A: 检查以下项目：
1. 确认AI服务已启动
2. 检查网络连接
3. 验证模型配置参数

**Q: 数据库错误**
A: 尝试以下解决方案：
```bash
# 删除数据库文件重新初始化
rm database/novel_workflow.db

# 重启服务
npm run dev
```

### 性能问题

**Q: 系统运行缓慢**
A: 优化建议：
1. 增加系统内存
2. 使用SSD存储
3. 关闭不必要的后台程序
4. 调整AI模型参数

## 升级指南

### 版本升级
```bash
# 备份数据
cp -r database database_backup
cp -r uploads uploads_backup

# 拉取最新代码
git pull origin main

# 更新依赖
npm run install-all

# 重启服务
npm run dev
```

### 数据迁移
如果需要迁移数据，请参考数据库迁移脚本。

## 卸载

### 完全卸载
```bash
# 停止服务
npm run stop  # 如果有

# 删除项目目录
rm -rf ai-novel-workflow

# 清理npm缓存（可选）
npm cache clean --force
```

## 技术支持

如果遇到安装问题，请：
1. 查看日志文件：`logs/app.log`
2. 检查系统要求是否满足
3. 参考常见问题解决方案
4. 提交Issue到项目仓库
