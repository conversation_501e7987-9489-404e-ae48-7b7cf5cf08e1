import { createRouter, createWebHistory } from 'vue-router'
import Projects from '@/views/Projects.vue'
import ChapterEditor from '@/views/ChapterEditor.vue'
import Settings from '@/views/Settings.vue'
import WorkflowDesigner from '@/views/WorkflowDesigner.vue'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/workflow'
    },
    {
      path: '/projects',
      name: 'Projects',
      component: Projects,
      meta: { title: '项目管理' }
    },
    {
      path: '/workflow',
      name: 'WorkflowDesigner',
      component: WorkflowDesigner,
      meta: { title: '工作流设计器' }
    },
    {
      path: '/chapters/:id/edit',
      name: 'ChapterEditor',
      component: ChapterEditor,
      meta: { title: '章节编辑器' }
    },
    {
      path: '/settings',
      name: 'Settings',
      component: Settings,
      meta: { title: '系统设置' }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - AI小说生成工作流系统`
  } else {
    document.title = 'AI小说生成工作流系统'
  }
  
  next()
})

export default router
