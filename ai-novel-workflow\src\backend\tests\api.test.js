const request = require('supertest');
const app = require('../app');
const { initDatabase } = require('../models');

describe('API测试', () => {
  beforeAll(async () => {
    // 初始化测试数据库
    process.env.DB_PATH = ':memory:';
    await initDatabase();
  });

  describe('健康检查', () => {
    test('GET /health 应该返回系统状态', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('项目管理 API', () => {
    let projectId;

    test('POST /api/projects 应该创建新项目', async () => {
      const projectData = {
        name: '测试项目',
        description: '这是一个测试项目',
        genre: '玄幻',
        tags: ['测试', '玄幻']
      };

      const response = await request(app)
        .post('/api/projects')
        .send(projectData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data.name).toBe(projectData.name);
      
      projectId = response.body.data.id;
    });

    test('GET /api/projects 应该返回项目列表', async () => {
      const response = await request(app)
        .get('/api/projects')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('projects');
      expect(response.body.data.projects).toBeInstanceOf(Array);
    });

    test('GET /api/projects/:id 应该返回项目详情', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.id).toBe(projectId);
    });

    test('PUT /api/projects/:id 应该更新项目', async () => {
      const updateData = {
        name: '更新后的项目名称',
        description: '更新后的描述'
      };

      const response = await request(app)
        .put(`/api/projects/${projectId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(updateData.name);
    });

    test('GET /api/projects/:id/stats 应该返回项目统计', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/stats`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('workflows');
      expect(response.body.data).toHaveProperty('characters');
      expect(response.body.data).toHaveProperty('chapters');
    });

    test('DELETE /api/projects/:id 应该删除项目', async () => {
      const response = await request(app)
        .delete(`/api/projects/${projectId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('角色管理 API', () => {
    let projectId, characterId;

    beforeAll(async () => {
      // 创建测试项目
      const projectResponse = await request(app)
        .post('/api/projects')
        .send({
          name: '角色测试项目',
          genre: '玄幻'
        });
      projectId = projectResponse.body.data.id;
    });

    test('POST /api/characters 应该创建新角色', async () => {
      const characterData = {
        project_id: projectId,
        name: '测试角色',
        role: 'protagonist',
        gender: 'male',
        age: 20,
        appearance: '英俊潇洒',
        personality: '勇敢正义',
        background: '普通出身'
      };

      const response = await request(app)
        .post('/api/characters')
        .send(characterData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(characterData.name);
      
      characterId = response.body.data.id;
    });

    test('GET /api/characters 应该返回角色列表', async () => {
      const response = await request(app)
        .get('/api/characters')
        .query({ project_id: projectId })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.data.length).toBeGreaterThan(0);
    });

    test('PUT /api/characters/:id 应该更新角色', async () => {
      const updateData = {
        personality: '更新后的性格描述'
      };

      const response = await request(app)
        .put(`/api/characters/${characterId}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.personality).toBe(updateData.personality);
    });
  });

  describe('工作流管理 API', () => {
    let projectId, workflowId;

    beforeAll(async () => {
      // 创建测试项目
      const projectResponse = await request(app)
        .post('/api/projects')
        .send({
          name: '工作流测试项目',
          genre: '玄幻'
        });
      projectId = projectResponse.body.data.id;
    });

    test('POST /api/workflows 应该创建新工作流', async () => {
      const workflowData = {
        project_id: projectId,
        name: '测试工作流',
        description: '这是一个测试工作流',
        type: 'custom',
        nodes: [
          {
            id: 'start',
            type: 'start',
            name: '开始',
            position: { x: 100, y: 100 }
          },
          {
            id: 'end',
            type: 'end',
            name: '结束',
            position: { x: 300, y: 100 }
          }
        ],
        edges: [
          {
            id: 'edge1',
            source: 'start',
            target: 'end'
          }
        ]
      };

      const response = await request(app)
        .post('/api/workflows')
        .send(workflowData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.name).toBe(workflowData.name);
      
      workflowId = response.body.data.id;
    });

    test('GET /api/workflows/:id 应该返回工作流详情', async () => {
      const response = await request(app)
        .get(`/api/workflows/${workflowId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('nodes');
      expect(response.body.data).toHaveProperty('edges');
    });

    test('POST /api/workflows/:id/execute 应该执行工作流', async () => {
      const executeData = {
        inputData: {
          test: 'value'
        }
      };

      const response = await request(app)
        .post(`/api/workflows/${workflowId}/execute`)
        .send(executeData)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('executionId');
    });
  });

  describe('AI服务 API', () => {
    test('GET /api/ai/status 应该返回AI状态', async () => {
      const response = await request(app)
        .get('/api/ai/status')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('status');
    });

    test('POST /api/ai/test 应该测试AI连接', async () => {
      const response = await request(app)
        .post('/api/ai/test')
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('设置管理 API', () => {
    test('GET /api/settings 应该返回设置列表', async () => {
      const response = await request(app)
        .get('/api/settings')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Object);
    });

    test('PUT /api/settings/:key 应该更新设置', async () => {
      const response = await request(app)
        .put('/api/settings/test_setting')
        .send({ value: 'test_value' })
        .expect(404); // 设置不存在

      expect(response.body.success).toBe(false);
    });
  });

  describe('错误处理', () => {
    test('GET /api/nonexistent 应该返回404', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body.success).toBe(false);
    });

    test('POST /api/projects 缺少必需字段应该返回400', async () => {
      const response = await request(app)
        .post('/api/projects')
        .send({}) // 缺少name字段
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});
