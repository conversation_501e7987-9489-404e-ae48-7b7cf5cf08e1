@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo      AI小说工作流系统 - 环境检查
echo ========================================
echo.

:: 设置当前目录
cd /d "%~dp0"

set ERROR_COUNT=0

:: 检查Node.js
echo [检查 1] Node.js环境
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装
    set /a ERROR_COUNT+=1
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do (
        echo ✅ Node.js版本: %%i
    )
)

:: 检查npm
echo [检查 2] npm环境
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装
    set /a ERROR_COUNT+=1
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do (
        echo ✅ npm版本: %%i
    )
)

:: 检查项目文件
echo [检查 3] 项目文件
if exist "package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在
    set /a ERROR_COUNT+=1
)

if exist "src" (
    echo ✅ src目录 存在
) else (
    echo ❌ src目录 不存在
    set /a ERROR_COUNT+=1
)

if exist "src\frontend\package.json" (
    echo ✅ 前端package.json 存在
) else (
    echo ❌ 前端package.json 不存在
    set /a ERROR_COUNT+=1
)

if exist "src\backend\package.json" (
    echo ✅ 后端package.json 存在
) else (
    echo ❌ 后端package.json 不存在
    set /a ERROR_COUNT+=1
)

:: 检查依赖安装
echo [检查 4] 依赖安装状态
if exist "node_modules" (
    echo ✅ 根目录依赖 已安装
) else (
    echo ⚠️ 根目录依赖 未安装
)

if exist "src\frontend\node_modules" (
    echo ✅ 前端依赖 已安装
) else (
    echo ⚠️ 前端依赖 未安装
)

if exist "src\backend\node_modules" (
    echo ✅ 后端依赖 已安装
) else (
    echo ⚠️ 后端依赖 未安装
)

:: 检查配置文件
echo [检查 5] 配置文件
if exist ".env" (
    echo ✅ .env文件 存在
) else (
    if exist ".env.example" (
        echo ⚠️ .env文件 不存在，但有示例文件
    ) else (
        echo ❌ .env相关文件 都不存在
        set /a ERROR_COUNT+=1
    )
)

:: 检查目录
echo [检查 6] 必要目录
if exist "database" (
    echo ✅ database目录 存在
) else (
    echo ⚠️ database目录 不存在
)

if exist "uploads" (
    echo ✅ uploads目录 存在
) else (
    echo ⚠️ uploads目录 不存在
)

if exist "logs" (
    echo ✅ logs目录 存在
) else (
    echo ⚠️ logs目录 不存在
)

:: 总结
echo.
echo ========================================
echo              检查结果
echo ========================================

if !ERROR_COUNT! equ 0 (
    echo ✅ 环境检查通过！
    echo.
    echo 您可以运行以下命令:
    echo - 安装依赖: install.bat
    echo - 启动系统: start.bat
    echo - 手动启动: npm run dev
) else (
    echo ❌ 发现 !ERROR_COUNT! 个严重问题
    echo.
    echo 建议操作:
    echo 1. 安装Node.js: https://nodejs.org/
    echo 2. 确保在正确的项目目录
    echo 3. 运行 install.bat 安装依赖
)

echo.
pause
