<template>
  <div class="projects">
    <!-- 导航栏 -->
    <div class="nav-bar">
      <el-button @click="router.push('/')" type="text">首页</el-button>
      <el-button @click="router.push('/projects')" type="primary">项目管理</el-button>
      <el-button @click="router.push('/settings')" type="text">系统设置</el-button>
    </div>

    <div class="page-header">
      <h1>📚 项目管理</h1>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        新建项目
      </el-button>
    </div>

    <!-- 项目列表 -->
    <div class="projects-grid">
      <el-card 
        v-for="project in projects" 
        :key="project.id" 
        class="project-card"
        @click="openProject(project)"
      >
        <template #header>
          <div class="card-header">
            <span>{{ project.name }}</span>
            <el-dropdown @command="handleCommand">
              <el-button type="text" @click.stop>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="`edit-${project.id}`">编辑</el-dropdown-item>
                  <el-dropdown-item :command="`delete-${project.id}`">删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
        
        <div class="project-info">
          <p class="description">{{ project.description || '暂无描述' }}</p>
          <div class="project-stats">
            <span>章节: {{ project.chapterCount || 0 }}</span>
            <span>字数: {{ formatWordCount(project.wordCount || 0) }}</span>
          </div>
          <div class="project-meta">
            <el-tag size="small" :type="getStatusType(project.status)">
              {{ getStatusText(project.status) }}
            </el-tag>
            <span class="update-time">{{ formatDate(project.updatedAt) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 空状态 -->
      <div v-if="projects.length === 0" class="empty-state">
        <el-empty description="还没有项目，创建第一个项目开始创作吧！">
          <el-button type="primary" @click="showCreateDialog = true">
            创建项目
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 创建项目对话框 -->
    <el-dialog v-model="showCreateDialog" title="创建新项目" width="500px">
      <el-form :model="newProject" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="项目名称" prop="name">
          <el-input v-model="newProject.name" placeholder="请输入项目名称" />
        </el-form-item>
        
        <el-form-item label="项目描述" prop="description">
          <el-input 
            v-model="newProject.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入项目描述"
          />
        </el-form-item>
        
        <el-form-item label="项目类型" prop="type">
          <el-select v-model="newProject.type" placeholder="选择项目类型">
            <el-option label="玄幻小说" value="fantasy" />
            <el-option label="都市小说" value="urban" />
            <el-option label="科幻小说" value="scifi" />
            <el-option label="历史小说" value="history" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="createProject" :loading="creating">
          创建
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MoreFilled } from '@element-plus/icons-vue'
import axios from 'axios'

const router = useRouter()

// 响应式数据
const projects = ref([])
const showCreateDialog = ref(false)
const creating = ref(false)
const formRef = ref()

const newProject = ref({
  name: '',
  description: '',
  type: ''
})

const rules = {
  name: [
    { required: true, message: '请输入项目名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择项目类型', trigger: 'change' }
  ]
}

// 加载项目列表
const loadProjects = async () => {
  try {
    const response = await axios.get('/api/projects')
    projects.value = response.data.data || []
  } catch (error) {
    console.error('加载项目失败:', error)
    // 如果API不可用，显示示例数据
    projects.value = [
      {
        id: 1,
        name: '我的第一部小说',
        description: '这是一个关于勇气与成长的故事',
        type: 'fantasy',
        status: 'active',
        chapterCount: 5,
        wordCount: 12500,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]
  }
}

// 创建项目
const createProject = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    creating.value = true
    
    // 尝试调用API
    try {
      const response = await axios.post('/api/projects', newProject.value)
      projects.value.unshift(response.data.data)
      ElMessage.success('项目创建成功！')
    } catch (error) {
      // API不可用时，添加到本地列表
      const project = {
        id: Date.now(),
        ...newProject.value,
        status: 'active',
        chapterCount: 0,
        wordCount: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }
      projects.value.unshift(project)
      ElMessage.success('项目创建成功！（演示模式）')
    }
    
    // 重置表单
    newProject.value = { name: '', description: '', type: '' }
    showCreateDialog.value = false
    
  } catch (error) {
    console.error('创建项目失败:', error)
  } finally {
    creating.value = false
  }
}

// 打开项目
const openProject = (project) => {
  // 跳转到章节编辑器
  router.push(`/chapters/new/edit?project=${project.id}`)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, id] = command.split('-')
  const projectId = parseInt(id)
  
  if (action === 'edit') {
    ElMessage.info('编辑功能开发中...')
  } else if (action === 'delete') {
    ElMessageBox.confirm('确定要删除这个项目吗？', '确认删除', {
      type: 'warning'
    }).then(() => {
      projects.value = projects.value.filter(p => p.id !== projectId)
      ElMessage.success('项目已删除')
    }).catch(() => {})
  }
}

// 工具函数
const getStatusType = (status) => {
  const types = {
    active: 'success',
    paused: 'warning',
    completed: 'info',
    archived: 'info'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    paused: '暂停',
    completed: '已完成',
    archived: '已归档'
  }
  return texts[status] || '未知'
}

const formatWordCount = (count) => {
  if (count >= 10000) {
    return `${(count / 10000).toFixed(1)}万字`
  }
  return `${count}字`
}

const formatDate = (date) => {
  if (!date) return ''
  const d = new Date(date)
  return `${d.getMonth() + 1}/${d.getDate()}`
}

// 组件挂载时加载数据
onMounted(() => {
  loadProjects()
})
</script>

<style scoped>
.projects {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.nav-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.project-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.project-info .description {
  color: #666;
  margin: 10px 0;
  min-height: 40px;
}

.project-stats {
  display: flex;
  gap: 15px;
  margin: 10px 0;
  font-size: 14px;
  color: #888;
}

.project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.update-time {
  font-size: 12px;
  color: #999;
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
}
</style>
