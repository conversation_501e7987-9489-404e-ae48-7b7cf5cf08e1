@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo    AI小说工作流系统 - 手动安装脚本
echo ========================================
echo.

:: 设置当前目录
cd /d "%~dp0"

echo 当前目录: %CD%
echo.

:: 步骤1: 检查Node.js
echo [步骤 1] 检查Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Node.js
    echo 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version 2^>nul') do echo ✅ Node.js: %%i
)

:: 步骤2: 检查npm
echo [步骤 2] 检查npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到npm
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('npm --version 2^>nul') do echo ✅ npm: %%i
)

:: 步骤3: 检查项目文件
echo [步骤 3] 检查项目文件...
if not exist "package.json" (
    echo ❌ 找不到package.json
    echo 请确保在项目根目录运行
    pause
    exit /b 1
)
echo ✅ 项目文件存在

:: 步骤4: 创建目录
echo [步骤 4] 创建必要目录...
if not exist "database" mkdir database
if not exist "uploads" mkdir uploads  
if not exist "logs" mkdir logs
echo ✅ 目录创建完成

:: 步骤5: 复制环境文件
echo [步骤 5] 设置环境文件...
if not exist ".env" (
    if exist ".env.example" (
        copy ".env.example" ".env" >nul 2>&1
        echo ✅ 环境文件已创建
    ) else (
        echo ⚠️ 未找到.env.example
    )
) else (
    echo ✅ 环境文件已存在
)

:: 步骤6: 安装根目录依赖
echo [步骤 6] 安装根目录依赖...
echo 执行: npm install
call npm install
if %errorlevel% neq 0 (
    echo ❌ 根目录依赖安装失败
    pause
    exit /b 1
)
echo ✅ 根目录依赖安装完成

:: 步骤7: 安装前端依赖
echo [步骤 7] 安装前端依赖...
if exist "src\frontend" (
    echo 进入目录: src\frontend
    cd src\frontend
    echo 执行: npm install
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..
    echo ✅ 前端依赖安装完成
) else (
    echo ⚠️ 前端目录不存在
)

:: 步骤8: 安装后端依赖
echo [步骤 8] 安装后端依赖...
if exist "src\backend" (
    echo 进入目录: src\backend
    cd src\backend
    echo 执行: npm install
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        cd ..\..
        pause
        exit /b 1
    )
    cd ..\..
    echo ✅ 后端依赖安装完成
) else (
    echo ⚠️ 后端目录不存在
)

echo.
echo ========================================
echo           安装完成！
echo ========================================
echo.
echo 现在您可以运行以下命令启动系统:
echo.
echo 方式1: 双击 start.bat
echo 方式2: 在命令行运行 npm run dev
echo.
echo 访问地址:
echo 前端: http://localhost:3000
echo 后端: http://localhost:8080
echo.
pause
