{"version": 3, "file": "eo.mjs", "sources": ["../../../../../packages/locale/lang/eo.ts"], "sourcesContent": ["export default {\n  name: 'eo',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Nun',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: '<PERSON>uli<PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>',\n      selectDate: '<PERSON><PERSON><PERSON> daton',\n      selectTime: '<PERSON><PERSON><PERSON> horon',\n      startDate: 'Komen<PERSON> Da<PERSON>',\n      startTime: 'Komenca Horo',\n      endDate: '<PERSON><PERSON> Dato',\n      endTime: '<PERSON>a Horo',\n      prevYear: 'Antaŭa Jaro',\n      nextYear: 'Sekva <PERSON>',\n      prevMonth: 'Anta<PERSON>a Mona<PERSON>',\n      nextMonth: 'Sek<PERSON>',\n      year: 'J<PERSON>',\n      month1: 'Janua<PERSON>',\n      month2: 'Februaro',\n      month3: 'Mart<PERSON>',\n      month4: 'Aprilo',\n      month5: 'Majo',\n      month6: 'Jun<PERSON>',\n      month7: '<PERSON>',\n      month8: 'Aŭgusto',\n      month9: 'Septembro',\n      month10: 'Okto<PERSON>',\n      month11: 'Novem<PERSON>',\n      month12: 'Decembro',\n      week: '<PERSON><PERSON><PERSON><PERSON>',\n      weeks: {\n        sun: 'Dim',\n        mon: 'Lun',\n        tue: 'Mar',\n        wed: 'Mer',\n        thu: 'Ĵaŭ',\n        fri: 'Ven',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aŭg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Ŝarĝante',\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      placeholder: 'Bonvolu elekti',\n    },\n    mention: {\n      loading: 'Ŝarĝante',\n    },\n    cascader: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      loading: 'Ŝarĝante',\n      placeholder: 'Bonvolu elekti',\n      noData: 'Neniuj datumoj',\n    },\n    pagination: {\n      goto: 'Iru al',\n      pagesize: '/ paĝo',\n      total: 'Entute {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaĝo',\n      confirm: 'Bone',\n      cancel: 'Nuligi',\n      error: 'Nevalida Enigo!',\n    },\n    upload: {\n      deleteTip: 'Premu \"Delete\" por forigi',\n      delete: 'Forigi',\n      preview: 'Antaŭrigardi',\n      continue: 'Daŭrigi',\n    },\n    table: {\n      emptyText: 'Neniuj datumoj',\n      confirmFilter: 'Konfirmi',\n      resetFilter: 'Restarigi',\n      clearFilter: 'Ĉiuj',\n      sumText: 'Sumo',\n    },\n    tree: {\n      emptyText: 'Neniuj datumoj',\n    },\n    transfer: {\n      noMatch: 'Neniuj kongruaj datumoj',\n      noData: 'Neniuj datumoj',\n      titles: ['Listo 1', 'Listo 2'],\n      filterPlaceholder: 'Enigu ŝlosilvorton',\n      noCheckedFormat: '{total} elementoj',\n      hasCheckedFormat: '{checked}/{total} elektitaj',\n    },\n    image: {\n      error: 'MALSUKCESIS',\n    },\n    pageHeader: {\n      title: 'Reen',\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,KAAK;AAChB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,kBAAkB;AAClC,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,eAAe;AAC5B,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,WAAW,EAAE,gBAAgB;AACnC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,2BAA2B;AAC5C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,QAAQ,EAAE,cAAc;AAC9B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,gBAAgB;AACjC,MAAM,aAAa,EAAE,UAAU;AAC/B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,gBAAgB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,yBAAyB;AAClD,MAAM,eAAe,EAAE,mBAAmB;AAC1C,MAAM,gBAAgB,EAAE,6BAA6B;AACrD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}