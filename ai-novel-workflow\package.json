{"name": "ai-novel-workflow", "version": "1.0.0", "description": "AI小说生成工作流系统", "main": "src/backend/app.js", "scripts": {"dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "backend:dev": "cd src\\backend && npm run dev", "frontend:dev": "cd src\\frontend && npm run dev", "build": "npm run frontend:build", "frontend:build": "cd src\\frontend && npm run build", "backend:build": "echo \"后端无需构建\"", "start": "npm run backend:start", "backend:start": "cd src\\backend && npm start", "install-all": "npm install && cd src\\frontend && npm install && cd ..\\..\\src\\backend && npm install", "test": "npm run backend:test", "backend:test": "cd src\\backend && npm test", "frontend:test": "cd src\\frontend && npm test", "check": "node scripts\\setup-check.js", "setup": "npm run install-all && npm run check"}, "keywords": ["ai", "novel", "workflow", "writing", "automation"], "author": "AI Novel Workflow Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-novel-workflow.git"}, "bugs": {"url": "https://github.com/your-username/ai-novel-workflow/issues"}, "homepage": "https://github.com/your-username/ai-novel-workflow#readme"}